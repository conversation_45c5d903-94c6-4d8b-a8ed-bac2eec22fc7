# Image Toolbox Application Structure
# This file documents the class structure and coding conventions for the Image Toolbox application

## Application Architecture
- The main application class is `ImageToolboxApp` which serves as the container for all tool frames
- Each tool is implemented as a separate frame class that inherits from `ttk.Frame`
- The application uses ttkbootstrap for styling (not ttkthemes)

## Main Classes
1. ImageToolboxApp - Main application container
   - Manages tabs, menu, status bar, and application lifecycle
   - Methods: create_tabs(), update_status(), set_icon(), create_menu_bar(), run()

2. UpscaleRemoverFrame - Tool for removing duplicate upscaled images
   - Methods: browse_folder_one(), browse_folder_two(), start_remove_duplicates(), run_remove_duplicates()

3. GemicaCaptions - Tool for generating image captions using Gemica
   - Methods: change_api_key(), browse_image_folder(), browse_output_csv(), start_captioning_thread(), run_gemica(), update_progress()

4. ImageMoverFrame - Tool for moving images between directories
   - Methods: browse_source(), browse_dest(), toggle_processing(), start_processing(), stop_processing(), scheduled_move(), process_images()

5. ImageSorterFrame - Tool for sorting images by various criteria
   - Methods: browse_source_folder(), browse_destination_folder(), start_sorting(), stop_sorting(), sort_images()

6. CSVSplitterFrame - Tool for splitting CSV files into smaller chunks
   - Methods: browse_csv_file(), browse_output_folder(), start_splitting(), split_csv()

7. WorkerControlFrame - Tool for controlling worker processes
   - Methods: start_workers(), stop_workers(), monitor_workers(), update_worker_status()

8. CSVConverter - Tool for converting between CSV formats
   - Methods: load_csv(), save_csv(), convert_format()

9. EmbedMetadata - Tool for embedding metadata into image files
   - Methods: load_image(), extract_metadata(), embed_metadata(), save_image()

10. ImageRenamer - Tool for batch renaming image files
    - Methods: browse_folder(), set_naming_pattern(), start_renaming(), rename_files()

11. PNGtoJPEGConverter - Tool for converting PNG images to JPEG format
    - Methods: browse_source_folder(), browse_output_folder(), set_quality(), start_conversion(), convert_images()

12. UnzipImages - Tool for extracting images from zip archives
    - Methods: browse_zip_file(), browse_extract_folder(), start_extraction(), extract_images()

13. OpenAICaptions - Tool for generating captions using OpenAI
    - Methods: set_api_key(), browse_image_folder(), browse_output_file(), start_captioning(), generate_captions()

## Imported Modules
- csv_converter: CSVConverter
- embed_metadata: EmbedMetadata
- image_renamer: ImageRenamer
- png_to_jpeg: PNGtoJPEGConverter
- unzip_images: UnzipImages
- openai_captions: OpenAICaptions
- gemica: GemicaCaptioner
- upscaleremover: remove_duplicate_files
- image_sorter: ImageSorterFrame
- csv_splitter: CSVSplitterFrame
- worker_control: WorkerControlFrame

## Coding Conventions
1. Class Structure:
   - Each tool frame should inherit from ttk.Frame
   - Constructor should take parent and app parameters
   - Should implement create_widgets() method for UI setup
   - Should implement tool-specific functionality methods

2. UI Conventions:
   - Use ttkbootstrap for styling (import ttkbootstrap as ttk)
   - Use LabelFrame for main sections with descriptive titles
   - Use consistent padding (10-15px) for frames and widgets
   - Use bootstyle constants for consistent styling (e.g., "info", "primary")

3. Threading:
   - Long-running operations should be executed in separate threads
   - Use app.update_status() to update the status bar
   - Implement progress reporting where applicable

4. Error Handling:
   - Use try/except blocks for file operations and external API calls
   - Report errors to the user via status updates or message boxes

## Adding New Tools
1. Create a new class that inherits from ttk.Frame
2. Implement __init__(self, parent, app) constructor
3. Implement create_widgets() method for UI setup
4. Implement tool-specific functionality
5. Add the new tool to ImageToolboxApp.create_tabs() method 