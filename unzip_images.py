import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import zipfile
import threading

class UnzipImages:
    def __init__(self, parent):
        self.frame = ttk.Frame(parent, padding=20)

        # Configure layout
        self.frame.grid_columnconfigure(0, weight=1)
        self.frame.grid_columnconfigure(1, weight=3)

        # Title
        title_label = ttk.Label(self.frame, text="Image Unzip Tool", font=("Helvetica", 16, "bold"), anchor="center")
        title_label.grid(row=0, column=0, columnspan=2, pady=10)

        # Input: Select ZIP Files Folder
        ttk.Label(self.frame, text="1. Select ZIP Files Folder:", font=("Arial", 11)).grid(row=1, column=0, sticky="w", pady=5)
        self.zip_folder_label = ttk.Label(self.frame, text="No folder selected.", anchor="w", relief="sunken")
        self.zip_folder_label.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        ttk.Button(self.frame, text="Browse", command=self.select_unzip_folder).grid(row=1, column=2, padx=5)

        # Input: Select Output Destination
        ttk.Label(self.frame, text="2. Select Output Destination:", font=("Arial", 11)).grid(row=2, column=0, sticky="w", pady=5)
        self.output_folder_label = ttk.Label(self.frame, text="No folder selected.", anchor="w", relief="sunken")
        self.output_folder_label.grid(row=2, column=1, sticky="ew", padx=5, pady=5)
        ttk.Button(self.frame, text="Browse", command=self.select_unzip_output_folder).grid(row=2, column=2, padx=5)

        # Progress Section
        ttk.Label(self.frame, text="3. Extraction Progress:", font=("Arial", 11)).grid(row=3, column=0, sticky="w", pady=10)
        self.progress_bar = ttk.Progressbar(self.frame, mode="determinate")
        self.progress_bar.grid(row=3, column=1, columnspan=2, sticky="ew", padx=5)

        self.progress_label = ttk.Label(self.frame, text="0%", anchor="center")
        self.progress_label.grid(row=4, column=1, pady=5)

        # Status Log Window
        self.log_window = tk.Text(self.frame, height=5, state="disabled", bg="#f7f7f7")
        self.log_window.grid(row=5, column=0, columnspan=3, sticky="ew", pady=10, padx=5)

        # Extract Button
        self.extract_button = ttk.Button(self.frame, text="Extract Images", command=self.extract_images_wrapper, style="Accent.TButton")
        self.extract_button.grid(row=6, column=0, columnspan=3, pady=20)

        # Paths
        self.unzip_folder_path = ""
        self.unzip_output_path = ""

        # Style
        style = ttk.Style()
        style.configure("TButton", padding=6, font=('Calibri', 11))
        style.configure("Accent.TButton", background="skyblue", font=('Calibri', 11, 'bold'))

    def log_message(self, message):
        self.log_window.config(state="normal")
        self.log_window.insert("end", message + "\n")
        self.log_window.see("end")
        self.log_window.config(state="disabled")

    def select_unzip_folder(self):
        self.unzip_folder_path = filedialog.askdirectory(title="Select Folder with ZIP Files")
        self.zip_folder_label.config(text=self.unzip_folder_path if self.unzip_folder_path else "No folder selected.")

    def select_unzip_output_folder(self):
        self.unzip_output_path = filedialog.askdirectory(title="Select Output Folder")
        self.output_folder_label.config(text=self.unzip_output_path if self.unzip_output_path else "No folder selected.")

    def extract_images_wrapper(self):
        if not self.unzip_folder_path or not self.unzip_output_path:
            messagebox.showerror("Error", "Please select both a ZIP folder and an output folder.")
            return

        self.progress_bar["value"] = 0
        self.progress_label.config(text="0%")
        self.log_window.config(state="normal")
        self.log_window.delete("1.0", "end")
        self.log_window.config(state="disabled")
        self.extract_button.config(state="disabled")
        threading.Thread(target=self._extract_images_threaded, args=(self.unzip_folder_path, self.unzip_output_path)).start()

    def _extract_images_threaded(self, zip_folder, output_folder):
        os.makedirs(output_folder, exist_ok=True)
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
        zip_files = [file for file in os.listdir(zip_folder) if file.endswith('.zip')]
        total_zips = len(zip_files)
        extracted_count = 0

        for idx, file_name in enumerate(zip_files):
            zip_path = os.path.join(zip_folder, file_name)
            try:
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    for file in zip_ref.namelist():
                        if any(file.lower().endswith(ext) for ext in image_extensions):
                            zip_ref.extract(file, output_folder)
                            extracted_count += 1
                            self.frame.after(0, lambda f=file: self.log_message(f"Extracted: {f}"))

            except Exception as e:
                self.frame.after(0, lambda: self.log_message(f"Error with ZIP file {file_name}: {e}"))

            progress = int(((idx + 1) / total_zips) * 100)
            self.frame.after(0, lambda p=progress: self.update_progress(p))

        self.frame.after(0, lambda: messagebox.showinfo("Success", f"Extraction complete! {extracted_count} images extracted."))
        self.frame.after(0, lambda: self.extract_button.config(state="normal"))

    def update_progress(self, value):
        self.progress_bar["value"] = value
        self.progress_label.config(text=f"{value}%")

if __name__ == '__main__':
    root = tk.Tk()
    root.title("ZIP File Extractor")
    root.geometry("700x500")

    notebook = ttk.Notebook(root)
    notebook.pack(expand=True, fill=tk.BOTH)
    unzip_images = UnzipImages(notebook)
    root.mainloop()
