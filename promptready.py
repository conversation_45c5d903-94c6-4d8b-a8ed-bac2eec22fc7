import tkinter as tk
from tkinter import filedialog, messagebox
import os
import hashlib
from PIL import Image  # Pillow library for image handling

def select_directory():
    """Opens a dialog to select a directory and updates the directory entry."""
    directory = filedialog.askdirectory()
    if directory:
        directory_entry.delete(0, tk.END)
        directory_entry.insert(0, directory)

def clean_and_deduplicate():
    """Cleans up the selected directory by removing non-JPEG files and duplicate JPEGs."""
    directory = directory_entry.get()
    if not directory:
        messagebox.showerror("Error", "Please select a directory.")
        return

    try:
        # 1. Remove non-JPEG files
        removed_files = remove_non_jpeg_files(directory)
        
        # 2. Remove duplicate JPEG files
        removed_dupes = remove_duplicate_jpeg_files(directory)

        message = f"Removed {len(removed_files)} non-JPEG files.\nRemoved {len(removed_dupes)} duplicate JPEG files."
        messagebox.showinfo("Success", message)
    except Exception as e:
        messagebox.showerror("Error", f"An error occurred: {e}")

def is_jpeg_file(filename):
    """Checks if the file is a JPEG based on its extension and content."""
    try:
        if not filename.lower().endswith(('.jpeg', '.jpg')):
            return False
        # try to open image using pillow
        Image.open(filename).verify()
        return True
    except Exception:
        return False

def remove_non_jpeg_files(directory):
    """Removes non-JPEG files from a directory. Returns a list of removed files."""
    removed_files = []
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath) and not is_jpeg_file(filepath):
            try:
                os.remove(filepath)
                removed_files.append(filename)
            except Exception as e:
                print(f"Error removing {filename}: {e}")
    return removed_files

def calculate_file_hash(filepath):
    """Calculates the SHA256 hash of a file."""
    hasher = hashlib.sha256()
    with open(filepath, 'rb') as file:
        while chunk := file.read(4096):
            hasher.update(chunk)
    return hasher.hexdigest()

def remove_duplicate_jpeg_files(directory):
    """Removes duplicate JPEG files within a directory based on their content.
    Returns a list of removed duplicate file names.
    """
    file_hashes = {}
    removed_dupes = []

    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath) and is_jpeg_file(filepath):
            file_hash = calculate_file_hash(filepath)
            if file_hash in file_hashes:
                try:
                  os.remove(filepath)
                  removed_dupes.append(filename)
                except Exception as e:
                  print(f"Error removing duplicate file {filename}: {e}")
            else:
                file_hashes[file_hash] = filename
    return removed_dupes

# Set up the Tkinter window
root = tk.Tk()
root.title("JPEG Cleaner")
root.geometry("500x200")

# Directory Selection
directory_label = tk.Label(root, text="Directory:")
directory_label.grid(row=0, column=0, padx=5, pady=5, sticky="e")

directory_entry = tk.Entry(root, width=50)
directory_entry.grid(row=0, column=1, padx=5, pady=5)

browse_button = tk.Button(root, text="Browse", command=select_directory)
browse_button.grid(row=0, column=2, padx=5, pady=5)

# Process Button
clean_button = tk.Button(root, text="Clean & Deduplicate", command=clean_and_deduplicate)
clean_button.grid(row=1, column=0, columnspan=3, pady=20)

root.mainloop()