import tkinter as tk
from tkinter import filedialog, messagebox, ttk, simpledialog
import pandas as pd
from iptcinfo3 import IPTCInfo
import os
import threading
from concurrent.futures import ThreadPoolExecutor
import queue
import time
import shutil
from PIL import Image, PngImagePlugin


class EmbedMetadata:
    def __init__(self, parent):
        self.frame = ttk.Frame(parent)

        # --- Settings Frame ---
        settings_frame = ttk.LabelFrame(self.frame, text="Settings")
        settings_frame.pack(pady=10, padx=20, fill=tk.X, expand=False)
        
         # Worker count setting
        ttk.Label(settings_frame, text="Worker Count:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.worker_count_var = tk.IntVar(value=4)  # Default worker count
        self.worker_count_entry = ttk.Entry(settings_frame, textvariable=self.worker_count_var, width=5)
        self.worker_count_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Button(settings_frame, text="Set", command=self.set_worker_count).grid(row=0, column=2, padx=5, pady=5)

         # Error Handling setting
        ttk.Label(settings_frame, text="Continue on Error:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.continue_on_error = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, variable=self.continue_on_error).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)

        # --- File Selection ---
        file_frame = ttk.LabelFrame(self.frame, text="File Selection")
        file_frame.pack(pady=10, padx=20, fill=tk.X, expand=False)

        ttk.Button(file_frame, text="Select CSV", command=self.select_embed_csv).grid(row=0, column=0, pady=10, padx=10)
        ttk.Button(file_frame, text="Select Image Folder", command=self.select_embed_folder).grid(row=0, column=1, pady=10, padx=10)
        ttk.Button(file_frame, text="Select Output Folder", command=self.select_output_folder).grid(row=0, column=2, pady=10, padx=10)


        # --- Status and Progress ---
        status_frame = ttk.LabelFrame(self.frame, text="Status")
        status_frame.pack(pady=10, padx=20, fill=tk.X, expand=True)

        self.embed_status_label = ttk.Label(status_frame, text="")
        self.embed_status_label.pack(pady=5, padx=20, fill=tk.X, expand=True)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, maximum=100, mode='determinate')
        self.progress_bar.pack(pady=5, padx=20, fill=tk.X, expand=True)

        # --- Start Button ---
        ttk.Button(self.frame, text="Embed Metadata", command=self.embed_metadata_wrapper).pack(pady=20, padx=20)
        
        self.embed_csv_path = ""
        self.embed_image_folder_path = ""
        self.output_folder_path = ""
        self.is_embedding = False
        self.task_queue = queue.Queue()
        self.executor = None

    def set_worker_count(self):
         try:
            count = int(self.worker_count_var.get())
            if count > 0:
               self.worker_count_var.set(count)
               if self.executor:
                   self.executor.shutdown(wait=False)
               self.executor = ThreadPoolExecutor(max_workers=count)

            else:
              messagebox.showerror("Error", "Worker count must be a positive integer.")
              self.worker_count_var.set(4)
         except ValueError:
            messagebox.showerror("Error", "Please enter a valid integer.")
            self.worker_count_var.set(4)



    def select_embed_csv(self):
        self.embed_csv_path = filedialog.askopenfilename(title="Select CSV file", filetypes=[("CSV files", "*.csv")])

    def select_embed_folder(self):
        self.embed_image_folder_path = filedialog.askdirectory(title="Select Image Folder")

    def select_output_folder(self):
        self.output_folder_path = filedialog.askdirectory(title="Select Output Folder for unmatched images")


    def embed_metadata_wrapper(self):
        if not self.embed_csv_path or not self.embed_image_folder_path:
            messagebox.showerror("Error", "Please select both a CSV file and an image folder.")
            return
        if self.is_embedding:
             messagebox.showinfo("Info", "Embedding is already in progress, wait to finish")
             return
        self.is_embedding = True
        self.embed_status_label.config(text="Preparing metadata...")
        self.progress_var.set(0)
        threading.Thread(target=self._prepare_and_embed_metadata).start()

    def _prepare_and_embed_metadata(self):
        try:
            df = pd.read_csv(self.embed_csv_path)
            #remove whitespace in the filename column
            df['Filename'] = df['Filename'].str.strip()
            total_images = len(df)
            if not self.executor:
               self.executor = ThreadPoolExecutor(max_workers=self.worker_count_var.get())
            self.task_queue = queue.Queue()

            csv_filenames = set(df['Filename'].tolist())
            all_image_files = [f for f in os.listdir(self.embed_image_folder_path)
                            if not f.startswith('.') and os.path.isfile(os.path.join(self.embed_image_folder_path, f))]

            unmatched_files = [f for f in all_image_files if f not in csv_filenames]
            
            # Check output folder and handle unmatched files
            if unmatched_files and self.output_folder_path:
                os.makedirs(self.output_folder_path, exist_ok=True)
                for file in unmatched_files:
                    source_path = os.path.join(self.embed_image_folder_path, file)
                    dest_path = os.path.join(self.output_folder_path, file)
                    shutil.move(source_path, dest_path)
                    self.embed_status_label.config(text=f"Moved unmatched file {file} to output folder.")
            elif unmatched_files:
                self.embed_status_label.config(text=f"Unmatched files found, but no output folder was provided.")
                messagebox.showwarning("Warning", "Unmatched files were found but no output folder was provided. These files were not processed.")


            for index, row in df.iterrows():
               self.task_queue.put((index, row, total_images))

            # Use futures to track progress
            futures = [self.executor.submit(self._embed_metadata_worker, self.embed_image_folder_path) for _ in range(min(self.worker_count_var.get(), total_images))]

            # Monitor progress
            while True:
                completed = sum(future.done() for future in futures)
                self.progress_var.set((completed/len(futures))*100 if futures else 100)
                if completed == len(futures):
                      break

                self.frame.update()
                time.sleep(0.1)

            self.embed_status_label.config(text="Metadata embedding completed.")
            messagebox.showinfo("Success", "Metadata embedding completed.")

        except Exception as e:
            self.embed_status_label.config(text=f"Error during metadata embedding: {e}")
            messagebox.showerror("Error", f"Error during metadata embedding: {e}")
        finally:
            self.is_embedding = False


    def _embed_metadata_worker(self, image_folder_path):
      while True:
         try:
            index, row, total_images= self.task_queue.get(block=False)
         except queue.Empty:
             break

         filename = row['Filename']
         title = row['Title']
         keywords = str(row['Keywords']).split(',') if pd.notna(row['Keywords']) and isinstance(row['Keywords'], str) else []
         category = str(row['Category']) if pd.notna(row['Category']) else ""
         image_path = os.path.join(image_folder_path, filename)

         if os.path.exists(image_path):
             try:
                 # Check if the file is a PNG
                 if filename.lower().endswith('.png'):
                     # Handle PNG files using PIL
                     self.embed_status_label.config(text=f"Processing PNG file {filename} ({index+1}/{total_images})")
                     try:
                         # Open the image with PIL
                         img = Image.open(image_path)
                         
                         # Create PNG info object
                         info = PngImagePlugin.PngInfo()
                         
                         # Add metadata
                         info.add_text("Title", title)
                         info.add_text("Description", title)  # Using title as description too
                         
                         # Add keywords as a comma-separated string
                         if keywords:
                             info.add_text("Keywords", ",".join(keywords))
                         
                         if category:
                             info.add_text("Category", category)
                         
                         # Save the image with metadata
                         img.save(image_path, "PNG", pnginfo=info)
                         
                         self.embed_status_label.config(text=f"Embedded metadata for PNG {filename} ({index+1}/{total_images})")
                     except Exception as e:
                         raise Exception(f"Error embedding PNG metadata: {e}")
                 else:
                     # Handle JPEG and other files using IPTCInfo
                     info = IPTCInfo(image_path)
                     if info:
                         info['object name'] = title
                         info['caption/abstract'] = title
                         info['keywords'] = keywords
                         info['category'] = category
                         info['supplemental category'] = [category]
                         info['subject reference'] = [category]
                         info.save()
                         if os.path.exists(image_path + "~"):
                             os.remove(image_path + "~")
                         self.embed_status_label.config(text=f"Embedded for {filename} ({index+1}/{total_images})")
                     else:
                        self.embed_status_label.config(text=f"No IPTC data in {filename}. ({index+1}/{total_images})")
             except Exception as e:
                    if self.continue_on_error.get():
                          self.embed_status_label.config(text=f"Error embedding for {filename}: {e} ({index+1}/{total_images}) - continuing")

                    else:
                       self.embed_status_label.config(text=f"Error embedding for {filename}: {e} ({index+1}/{total_images}) - stopped")
                       self.task_queue.queue.clear()
                       break
         else:
             self.embed_status_label.config(text=f"Image {filename} not found ({index+1}/{total_images})")


         self.task_queue.task_done()



if __name__ == '__main__':
    root = tk.Tk()
    root.title("Metadata Embedder")
    notebook = ttk.Notebook(root)
    notebook.pack(expand=True, fill=tk.BOTH)
    embed_metadata = EmbedMetadata(notebook)
    root.mainloop()