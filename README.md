# Image Sorter with Gemini AI

This tool uses Google's Gemini AI to analyze images and sort them into different folders based on their potential as stock images and their quality.

## Features

- Analyzes images using Gemini AI's vision capabilities
- Sorts images into categories:
  - High/Low stock image potential
  - Good/Bad image quality
- Provides detailed logging of the analysis process
- Handles multiple image formats (PNG, JPG, JPEG, GIF, BMP)

## Setup

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your Gemini API key:
   - Get your API key from Google AI Studio (https://makersuite.google.com/app/apikey)
   - Set it as an environment variable:
     ```bash
     export GEMINI_API_KEY='your-api-key-here'
     ```

3. Prepare your images:
   - Create an `input_images` folder in the same directory as the script
   - Place all images you want to analyze in this folder

## Usage

1. Run the script:
```bash
python image_sorter.py
```

2. The script will:
   - Process all images in the `input_images` folder
   - Create sorted folders under `sorted_images/`:
     - `high_potential/`: Images with high stock photo potential
     - `low_potential/`: Images with low stock photo potential
     - `good_quality/`: Images with good technical quality
     - `bad_quality/`: Images with poor technical quality
   - Generate a log file (`image_sorter.log`) with detailed processing information

## Output Structure

```
.
├── input_images/
│   └── (your images here)
├── sorted_images/
│   ├── high_potential/
│   ├── low_potential/
│   ├── good_quality/
│   └── bad_quality/
└── image_sorter.log
```

## Notes

- Images are copied (not moved) to their respective folders
- Each image can appear in two folders (one for potential and one for quality)
- Check the log file for detailed processing information and any errors 