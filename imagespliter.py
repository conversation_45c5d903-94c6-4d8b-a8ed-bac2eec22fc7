import os
import shutil
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import threading
import math
from PIL import Image


class ImageSplitterFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        self.source_dir = ""
        self.dest_parent_dir = ""
        self.images_per_folder = 100
        self.is_running = False
        self.image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff']
        self.create_widgets()

    def create_widgets(self):
        # Main frame layout with improved styling
        main_frame = ttk.LabelFrame(self, text="Image Folder Splitter", padding=15, bootstyle="info")
        main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # Description
        description = ttk.Label(
            main_frame,
            text="Split a large image folder into multiple smaller folders with specified number of images.",
            wraplength=500,
            justify=tk.LEFT,
            padding=(0, 0, 0, 10)
        )
        description.pack(fill=tk.X)

        # Input section
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=10)

        # Source Folder Selection
        source_frame = ttk.Frame(input_frame)
        source_frame.pack(fill=tk.X, pady=5)

        source_label = ttk.Label(source_frame, text="Source Folder:", width=15, anchor=tk.W)
        source_label.pack(side=tk.LEFT, padx=5)

        self.source_entry = ttk.Entry(source_frame)
        self.source_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        source_button = ttk.Button(
            source_frame,
            text="Browse",
            command=self.browse_source,
            bootstyle=SUCCESS,
            width=10
        )
        source_button.pack(side=tk.LEFT, padx=5)

        # Destination Parent Folder Selection
        dest_frame = ttk.Frame(input_frame)
        dest_frame.pack(fill=tk.X, pady=5)

        dest_label = ttk.Label(dest_frame, text="Output Folder:", width=15, anchor=tk.W)
        dest_label.pack(side=tk.LEFT, padx=5)

        self.dest_entry = ttk.Entry(dest_frame)
        self.dest_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        dest_button = ttk.Button(
            dest_frame,
            text="Browse",
            command=self.browse_destination,
            bootstyle=SUCCESS,
            width=10
        )
        dest_button.pack(side=tk.LEFT, padx=5)

        # Settings Frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding=10)
        settings_frame.pack(fill=tk.X, pady=10)

        # Images per folder
        images_per_folder_frame = ttk.Frame(settings_frame)
        images_per_folder_frame.pack(fill=tk.X, pady=5)

        images_label = ttk.Label(
            images_per_folder_frame,
            text="Images per folder:",
            width=15,
            anchor=tk.W
        )
        images_label.pack(side=tk.LEFT, padx=5)

        self.images_per_folder_var = tk.IntVar(value=self.images_per_folder)
        images_spinbox = ttk.Spinbox(
            images_per_folder_frame,
            from_=1,
            to=1000,
            textvariable=self.images_per_folder_var,
            width=10
        )
        images_spinbox.pack(side=tk.LEFT, padx=5)

        # Sort options
        sort_frame = ttk.Frame(settings_frame)
        sort_frame.pack(fill=tk.X, pady=5)

        sort_label = ttk.Label(sort_frame, text="Sort by:", width=15, anchor=tk.W)
        sort_label.pack(side=tk.LEFT, padx=5)

        self.sort_option = tk.StringVar(value="name")
        sort_name = ttk.Radiobutton(
            sort_frame,
            text="Filename",
            variable=self.sort_option,
            value="name"
        )
        sort_name.pack(side=tk.LEFT, padx=5)

        sort_date = ttk.Radiobutton(
            sort_frame,
            text="Date modified",
            variable=self.sort_option,
            value="date"
        )
        sort_date.pack(side=tk.LEFT, padx=5)

        sort_size = ttk.Radiobutton(
            sort_frame,
            text="File size",
            variable=self.sort_option,
            value="size"
        )
        sort_size.pack(side=tk.LEFT, padx=5)

        # Copy or Move options
        operation_frame = ttk.Frame(settings_frame)
        operation_frame.pack(fill=tk.X, pady=5)

        operation_label = ttk.Label(operation_frame, text="Operation:", width=15, anchor=tk.W)
        operation_label.pack(side=tk.LEFT, padx=5)

        self.operation_option = tk.StringVar(value="copy")
        copy_radio = ttk.Radiobutton(
            operation_frame,
            text="Copy files",
            variable=self.operation_option,
            value="copy"
        )
        copy_radio.pack(side=tk.LEFT, padx=5)

        move_radio = ttk.Radiobutton(
            operation_frame,
            text="Move files",
            variable=self.operation_option,
            value="move"
        )
        move_radio.pack(side=tk.LEFT, padx=5)

        # Progress section
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding=10)
        progress_frame.pack(fill=tk.X, pady=10)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            orient="horizontal",
            length=100,
            mode="determinate",
            variable=self.progress_var,
            bootstyle="success"
        )
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        self.status_label = ttk.Label(
            progress_frame,
            text="Ready",
            justify=tk.CENTER
        )
        self.status_label.pack(fill=tk.X, padx=5, pady=5)

        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        self.start_button = ttk.Button(
            button_frame,
            text="Start Splitting",
            command=self.start_splitting,
            bootstyle="primary",
            width=15
        )
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel_splitting,
            state="disabled",
            bootstyle="danger-outline",
            width=15
        )
        self.cancel_button.pack(side=tk.LEFT, padx=5)

    def browse_source(self):
        folder = filedialog.askdirectory(title="Select Source Image Folder")
        if folder:
            self.source_dir = folder
            self.source_entry.delete(0, tk.END)
            self.source_entry.insert(0, folder)
            self.status_label.config(text=f"Selected source: {os.path.basename(folder)}")

    def browse_destination(self):
        folder = filedialog.askdirectory(title="Select Output Parent Folder")
        if folder:
            self.dest_parent_dir = folder
            self.dest_entry.delete(0, tk.END)
            self.dest_entry.insert(0, folder)
            self.status_label.config(text=f"Selected destination: {os.path.basename(folder)}")

    def start_splitting(self):
        """Start the splitting process"""
        # Validate inputs
        self.source_dir = self.source_entry.get()
        self.dest_parent_dir = self.dest_entry.get()
        self.images_per_folder = self.images_per_folder_var.get()

        if not self.source_dir or not os.path.isdir(self.source_dir):
            messagebox.showerror("Error", "Please select a valid source folder.")
            return

        if not self.dest_parent_dir or not os.path.isdir(self.dest_parent_dir):
            messagebox.showerror("Error", "Please select a valid destination folder.")
            return

        if self.images_per_folder <= 0:
            messagebox.showerror("Error", "Images per folder must be greater than 0.")
            return

        # Confirm if source and destination are the same
        if os.path.samefile(self.source_dir, self.dest_parent_dir):
            if not messagebox.askyesno("Warning", 
                "Source and destination folders are the same. This may cause issues.\nDo you want to continue?"):
                return

        # Update UI
        self.start_button.config(state="disabled")
        self.cancel_button.config(state="normal")
        self.is_running = True
        self.progress_var.set(0)
        self.status_label.config(text="Starting...")

        # Start splitting in a separate thread
        threading.Thread(target=self.split_images, daemon=True).start()

    def cancel_splitting(self):
        """Cancel the splitting process"""
        if self.is_running:
            self.is_running = False
            self.status_label.config(text="Cancelling...")

    def split_images(self):
        """Split the images into multiple folders"""
        try:
            # Get all image files from source directory
            all_files = []
            for filename in os.listdir(self.source_dir):
                file_path = os.path.join(self.source_dir, filename)
                if os.path.isfile(file_path) and self.is_image_file(file_path):
                    all_files.append(file_path)

            if not all_files:
                self.update_status("No image files found in the source folder.")
                self.finish_process()
                return

            # Sort files based on selected option
            sort_option = self.sort_option.get()
            if sort_option == "name":
                all_files.sort()
            elif sort_option == "date":
                all_files.sort(key=os.path.getmtime)
            elif sort_option == "size":
                all_files.sort(key=os.path.getsize)

            # Calculate number of folders needed
            total_files = len(all_files)
            num_folders = math.ceil(total_files / self.images_per_folder)
            
            self.update_status(f"Found {total_files} images. Creating {num_folders} folders...")
            
            # Create base folder name from source directory name
            source_name = os.path.basename(self.source_dir)
            
            # Process files in batches
            for folder_idx in range(num_folders):
                if not self.is_running:
                    self.update_status("Operation cancelled.")
                    break
                
                # Create folder name with index
                folder_name = f"{source_name}_{folder_idx + 1:03d}"
                folder_path = os.path.join(self.dest_parent_dir, folder_name)
                
                # Create folder if it doesn't exist
                os.makedirs(folder_path, exist_ok=True)
                
                # Calculate start and end indices for this batch
                start_idx = folder_idx * self.images_per_folder
                end_idx = min(start_idx + self.images_per_folder, total_files)
                
                # Process files for this folder
                for file_idx in range(start_idx, end_idx):
                    if not self.is_running:
                        break
                    
                    source_file = all_files[file_idx]
                    file_name = os.path.basename(source_file)
                    dest_file = os.path.join(folder_path, file_name)
                    
                    # Copy or move the file based on selected operation
                    operation = self.operation_option.get()
                    try:
                        if operation == "copy":
                            shutil.copy2(source_file, dest_file)
                        else:  # move
                            shutil.move(source_file, dest_file)
                    except Exception as e:
                        self.app.update_status(f"Error processing {file_name}: {str(e)}")
                    
                    # Update progress
                    progress = (file_idx + 1) / total_files * 100
                    self.update_progress(progress)
                    
                    # Update status periodically
                    if (file_idx - start_idx) % 10 == 0 or file_idx == end_idx - 1:
                        self.update_status(f"Processing folder {folder_idx + 1}/{num_folders}: {file_idx - start_idx + 1}/{end_idx - start_idx} files")
            
            if self.is_running:
                self.update_status(f"Completed! Split {total_files} images into {num_folders} folders.")
                
        except Exception as e:
            self.app.update_status(f"Error: {str(e)}")
            self.update_status(f"Error occurred: {str(e)}")
        
        self.finish_process()

    def update_progress(self, value):
        """Update the progress bar"""
        self.progress_var.set(value)
        
        # Force UI update
        try:
            self.update_idletasks()
        except:
            pass

    def update_status(self, message):
        """Update the status label"""
        self.status_label.config(text=message)
        self.app.update_status(message)
        
        # Force UI update
        try:
            self.update_idletasks()
        except:
            pass

    def finish_process(self):
        """Reset UI after process completes or is cancelled"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.cancel_button.config(state="disabled")

    def is_image_file(self, file_path):
        """Check if a file is an image based on extension"""
        ext = os.path.splitext(file_path)[1].lower()
        if ext in self.image_extensions:
            # Verify it's a valid image by trying to open it
            try:
                with Image.open(file_path) as img:
                    return True
            except:
                return False
        return False
