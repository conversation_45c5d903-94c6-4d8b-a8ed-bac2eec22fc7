import tkinter as tk
from tkinter import ttk, filedialog, messagebox
# from ttkthemes import ThemedTk #removed since we will be using ttkbootstrap
import ttkbootstrap as ttk
from ttkbootstrap.constants import * #add for ttkboot strap
import os
import sys
import threading
import logging
import time
import shutil
from PIL import Image

# Define basic theme colors directly
class AppTheme:
    COLORS = {
        'primary': '#0d6efd',
        'success': '#198754',
        'info': '#0dcaf0',
        'warning': '#ffc107',
        'danger': '#dc3545'
    }
    
    @staticmethod
    def apply_theme(style):
        """Apply basic theme styling"""
        pass

# Specify the absolute path to your modules
MODULE_PATH = '/Users/<USER>/Documents/rebildeing/APP v4'
sys.path.insert(0, MODULE_PATH)

# Import tool modules using the adjusted path
from csv_converter import CSVConverter
from embed_metadata import EmbedMetadata
from image_renamer import ImageRenamer
from png_to_jpeg import PNGtoJPEGConverter
from unzip_images import UnzipImages
from upscaleremover import remove_duplicate_files
from csv_splitter import CSVSplitterFrame
from image_cleaner import ImageCleanerFrame  # Import ImageCleanerFrame
from imagespliter import ImageSplitterFrame  # Import the new ImageSplitterFrame
from image_resizer import ImageResizerFrame  # Import the new ImageResizerFrame


class UpscaleRemoverFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        self.create_widgets()

    def create_widgets(self):
        # Main frame layout with improved styling
        main_frame = ttk.LabelFrame(self, text="Upscale Remover", padding=15, style="info.TLabelframe")
        main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # Warning label
        warning_label = ttk.Label(
            main_frame,
            text="⚠️ WARNING: Duplicates will be removed from Folder One!",
            foreground="red",
            font=("Arial", 12, "bold")
        )
        warning_label.pack(pady=(0, 20))

        # Folder selection inputs
        folder_frame = ttk.Frame(main_frame)
        folder_frame.pack(pady=15, fill=tk.X)

        # Folder One Selection
        folder_one_label = ttk.Label(folder_frame, text="Folder One (Source):", font=("Helvetica", 12, "bold"))
        folder_one_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=8)

        self.folder_one_entry = ttk.Entry(folder_frame, font=("Helvetica", 12))
        self.folder_one_entry.grid(row=0, column=1, padx=5, pady=8, sticky=tk.EW)

        folder_one_browse = ttk.Button(folder_frame, text="Browse", command=self.browse_folder_one, 
                                     style="success.TButton")
        folder_one_browse.grid(row=0, column=2, padx=5, pady=8)

        # Folder Two Selection
        folder_two_label = ttk.Label(folder_frame, text="Folder Two (Reference):", font=("Helvetica", 12, "bold"))
        folder_two_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=8)

        self.folder_two_entry = ttk.Entry(folder_frame, font=("Helvetica", 12))
        self.folder_two_entry.grid(row=1, column=1, padx=5, pady=8, sticky=tk.EW)

        folder_two_browse = ttk.Button(folder_frame, text="Browse", command=self.browse_folder_two, 
                                     style="success.TButton")
        folder_two_browse.grid(row=1, column=2, padx=5, pady=8)

        # Configure columns for responsive resizing
        folder_frame.columnconfigure(1, weight=1)

        # Description
        description = """
        How it works:
        1. Select two folders using the Browse buttons
        2. The app will find images with matching names (ignoring extensions)
        3. Matching images will be REMOVED from Folder One
        4. Folder Two remains unchanged
        """
        desc_label = ttk.Label(main_frame, text=description, justify=tk.LEFT, 
                             font=("Helvetica", 10))
        desc_label.pack(pady=20, anchor=tk.W)

        # Process button
        process_btn = ttk.Button(
            main_frame,
            text="Remove Duplicates",
            command=self.start_remove_duplicates,
            style="primary-outline.TButton",
            width=20
        )
        process_btn.pack(pady=20)

        # Status frame
        status_frame = ttk.Frame(main_frame, style="light.TFrame")
        status_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.status_label = ttk.Label(status_frame, text="Ready", font=("Helvetica", 10))
        self.status_label.pack(pady=5)

    def browse_folder_one(self):
        folder_selected = filedialog.askdirectory()
        if folder_selected:
            self.folder_one_entry.delete(0, tk.END)
            self.folder_one_entry.insert(0, folder_selected)
            self.status_label.config(text=f"Selected source folder: {os.path.basename(folder_selected)}")

    def browse_folder_two(self):
        folder_selected = filedialog.askdirectory()
        if folder_selected:
            self.folder_two_entry.delete(0, tk.END)
            self.folder_two_entry.insert(0, folder_selected)
            self.status_label.config(text=f"Selected destination folder: {os.path.basename(folder_selected)}")

    def start_remove_duplicates(self):
        folder_one = self.folder_one_entry.get()
        folder_two = self.folder_two_entry.get()

        if not folder_one or not folder_two:
            messagebox.showerror("Error", "Please select both folders.")
            return

        if messagebox.askyesno("Confirm", 
                              "This will permanently delete duplicate images from Folder One.\n"
                              "Are you sure you want to continue?"):
            self.status_label.config(text="Processing... Please wait")
            threading.Thread(target=self.run_remove_duplicates, args=(folder_one, folder_two), daemon=True).start()

    def run_remove_duplicates(self, folder_one, folder_two):
        try:
            removed_count, error_messages = remove_duplicate_files(folder_one, folder_two)
            
            # Schedule UI updates in the main thread
            self.app.root.after(0, self._update_ui_after_removal, removed_count, error_messages)
        except Exception as e:
            # Schedule error handling in the main thread
            self.app.root.after(0, self._handle_removal_error, str(e))
    
    def _update_ui_after_removal(self, removed_count, error_messages):
        """Handle UI updates after removal process (runs in main thread)"""
        if error_messages:
            for error in error_messages:
                self.app.update_status(f"Error: {error}")
            messagebox.showerror("Error", f"Encountered Error: {error_messages}")
            self.status_label.config(text="Process failed - see error details")
        else:
            self.app.update_status(f"Removed {removed_count} duplicate files.")
            messagebox.showinfo("Success", f"Removed {removed_count} duplicate files.")
            self.status_label.config(text=f"Successfully removed {removed_count} files")
    
    def _handle_removal_error(self, error_message):
        """Handle unexpected errors during removal process (runs in main thread)"""
        self.app.update_status(f"Error: {error_message}")
        messagebox.showerror("Error", f"An unexpected error occurred: {error_message}")
        self.status_label.config(text="Process failed - unexpected error")


class ImageMoverFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        self.source_dir = ""
        self.destination_dir = ""
        self.running = False
        self.image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp']
        self.interval = 600  # 10 minutes in seconds
        self.create_widgets()

    def create_widgets(self):
        # Main frame layout with improved styling
        main_frame = ttk.LabelFrame(self, text="Image Mover (Keep Recent)", padding=15, style="info.TLabelframe")
        main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # Source Folder Selection
        source_frame = ttk.Frame(main_frame)
        source_frame.pack(pady=5, fill=tk.X)

        ttk.Label(source_frame, text="Source Folder:", font=("Helvetica", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.source_entry = ttk.Entry(source_frame, width=50)
        self.source_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(source_frame, text="Browse", command=self.browse_source, style="success.TButton").grid(row=0, column=2, padx=5)

        # Destination Folder Selection
        dest_frame = ttk.Frame(main_frame)
        dest_frame.pack(pady=5, fill=tk.X)

        ttk.Label(dest_frame, text="Destination Folder:", font=("Helvetica", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.dest_entry = ttk.Entry(dest_frame, width=50)
        self.dest_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(dest_frame, text="Browse", command=self.browse_dest, style="success.TButton").grid(row=0, column=2, padx=5)

        # Interval Setting
        interval_frame = ttk.Frame(main_frame)
        interval_frame.pack(pady=5, fill=tk.X)

        ttk.Label(interval_frame, text="Check Interval (minutes):", font=("Helvetica", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.interval_entry = ttk.Entry(interval_frame, width=10)
        self.interval_entry.insert(0, str(self.interval // 60))
        self.interval_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Start/Stop Button
        self.start_button = ttk.Button(main_frame, text="Start Processing", command=self.toggle_processing, style="primary.TButton", width=20)
        self.start_button.pack(pady=10)

        # Status Label
        self.status_label = ttk.Label(main_frame, text="Waiting...", font=("Helvetica", 10))
        self.status_label.pack(pady=5)

    def browse_source(self):
        folder = filedialog.askdirectory()
        if folder:
            self.source_dir = folder
            self.source_entry.delete(0, tk.END)
            self.source_entry.insert(0, folder)

    def browse_dest(self):
        folder = filedialog.askdirectory()
        if folder:
            self.destination_dir = folder
            self.dest_entry.delete(0, tk.END)
            self.dest_entry.insert(0, folder)

    def toggle_processing(self):
        if not self.running:
            try:
                interval_minutes = int(self.interval_entry.get())
                if interval_minutes <= 0:
                    messagebox.showerror("Error", "Interval must be greater than 0 minutes.")
                    return
                self.interval = interval_minutes * 60
            except ValueError:
                messagebox.showerror("Error", "Invalid interval. Please enter a valid number of minutes.")
                return

            if not self.source_dir or not self.destination_dir:
                messagebox.showerror("Error", "Please select both source and destination folders.")
                return
            if not os.path.isdir(self.source_dir):
                messagebox.showerror("Error", "Source folder not valid.")
                return
            if not os.path.isdir(self.destination_dir):
                messagebox.showerror("Error", "Destination folder not valid.")
                return
            self.start_processing()
        else:
            self.stop_processing()

    def start_processing(self):
        self.running = True
        self.start_button.config(text="Stop Processing")
        self.status_label.config(text=f"Processing Scheduled (Every {self.interval//60} minutes)...")
        self.thread = threading.Thread(target=self.scheduled_move)
        self.thread.daemon = True
        self.thread.start()
        logging.info(f"Scheduled processing started with {self.interval//60} minute interval.")

    def stop_processing(self):
        self.running = False
        self.start_button.config(text="Start Processing")
        self.status_label.config(text="Stopped")
        logging.info("Scheduled processing stopped.")

    def scheduled_move(self):
        while self.running:
            logging.info("Scheduled processing tick.")
            self.process_images()
            time.sleep(self.interval)

    def process_images(self):
        if not self.source_dir or not self.destination_dir:
            return

        files_to_move = []
        error_files = []
        
        for filename in os.listdir(self.source_dir):
            file_path = os.path.join(self.source_dir, filename)
            if os.path.isfile(file_path) and any(filename.lower().endswith(ext) for ext in self.image_extensions):
                try:
                    with PIL.Image.open(file_path) as img:
                        files_to_move.append((file_path, os.path.getmtime(file_path)))
                except Exception as e:
                    error_files.append(f"File {filename} error: {e}")
                    logging.error(f"Error checking {filename}: {e}")

        if not files_to_move:
            if error_files:
                # Schedule UI update in the main thread
                self.app.root.after(0, lambda: self._update_status(
                    f"Found errors: {len(error_files)}. No image found to be moved.", 
                    "danger"
                ))
                logging.info(f"No valid images to move after detection. Number of images which could not be checked : {len(error_files)}")
            else:
                logging.info(f"No valid images to move")
            return

        # Sort files by modification time (newest first)
        files_to_move.sort(key=lambda item: item[1], reverse=True)
        
        # Keep the 10 most recent files, move the rest
        files_to_keep = files_to_move[:10]
        files_to_move = files_to_move[10:]
        
        moved_count = 0
        move_errors = []
        
        for file_path, _ in files_to_move:
            filename = os.path.basename(file_path)
            try:
                new_path = os.path.join(self.destination_dir, filename)
                shutil.move(file_path, new_path)
                moved_count += 1
            except Exception as e:
                move_errors.append(f"Failed to move {filename}: {e}")
                logging.error(f"Error moving {filename}: {e}")

        # Schedule UI update in the main thread
        self.app.root.after(0, lambda: self._update_process_results(
            moved_count, len(files_to_keep), move_errors
        ))
        logging.info(f"Moved {moved_count} image(s), kept {len(files_to_keep)} recent images. {len(move_errors)} move errors in this interval.")

    def _update_status(self, message, status_type):
        """Update status label with message and status type (runs in main thread)"""
        self.status_label.config(text=message)
        
    def _update_process_results(self, moved_count, kept_count, move_errors):
        """Update UI with process results (runs in main thread)"""
        if moved_count == 0:
            if move_errors:
                self.status_label.config(text=f"No images moved, encountered errors: {len(move_errors)}")
            else:
                self.status_label.config(text=f"No images to move. Keeping {kept_count} recent images.")
        else:
            self.status_label.config(
                text=f"Moved {moved_count} image(s), kept {kept_count} recent images. {len(move_errors)} errors occurred."
            )


class ImageToolboxApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Toolbox Professional")
        
        # Set window size and minimum size
        self.root.geometry("1280x900")
        self.root.minsize(1000, 700)
        
        # Configure the theme
        self.style = ttk.Style()
        AppTheme.apply_theme(self.style)
        
        # Current active tool frame
        self.active_tool = None
        self.tool_frames = {}
        
        # Create the main application layout
        self.setup_layout()
        
        # Create tools
        self.create_tools()
    
    def setup_layout(self):
        """Create the main application layout structure"""
        # Main container with padding and card styling
        self.main_container = ttk.Frame(self.root, padding="10", style='Card.TFrame')
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create header with app title and controls
        self.create_header()
        
        # Create main content area with sidebar and tool frames
        self.content_area = ttk.Frame(self.main_container)
        self.content_area.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Create sidebar for tool selection
        self.create_sidebar()
        
        # Create tool container area
        self.tool_container = ttk.Frame(self.content_area)
        self.tool_container.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create status bar
        self.create_status_bar()
    
    def create_header(self):
        """Create a simple header with app title"""
        # Simple header with just title
        self.header_frame = ttk.Frame(self.main_container)
        self.header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # App title
        app_title = ttk.Label(
            self.header_frame,
            text="Image Toolbox",
            font=("Arial", 16, "bold")
        )
        app_title.pack(pady=10)
    
    def create_sidebar(self):
        """Create a simple sidebar with tool buttons"""
        # Simple sidebar frame
        self.sidebar = ttk.Frame(self.content_area, width=200)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        self.sidebar.pack_propagate(False)
        
        # Create frame for sidebar content
        self.sidebar_frame = ttk.Frame(self.sidebar)
        self.sidebar_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_status_bar(self):
        """Create a simple status bar"""
        # Simple status bar
        self.status_frame = ttk.Frame(self.main_container)
        self.status_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Status label
        self.status_label = ttk.Label(
            self.status_frame,
            text="Ready",
            font=("Arial", 10)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

    def create_tools(self):
        """Create tool frames and sidebar buttons"""
        # Define tool list - simplified
        tools = [
            ("CSV Converter", CSVConverter, False),
            ("CSV Splitter", CSVSplitterFrame, True),
            ("Embed Metadata", EmbedMetadata, False),
            ("Image Renamer", ImageRenamer, False),
            ("PNG to JPEG", PNGtoJPEGConverter, False),
            ("Unzip Images", UnzipImages, False),
            ("Image Mover", ImageMoverFrame, True),
            ("Image Cleaner", ImageCleanerFrame, True),
            ("Image Splitter", ImageSplitterFrame, True),
            ("Image Resizer", ImageResizerFrame, True),
            ("Upscale Remover", UpscaleRemoverFrame, True)
        ]
        
        # Create tool buttons
        for tool_name, tool_class, needs_app in tools:
            self.create_tool_button(self.sidebar_frame, tool_name, tool_class, needs_app)
        
        # Show the first tool by default
        if self.tool_frames:
            first_tool = next(iter(self.tool_frames.values()))
            self.show_tool(first_tool["name"])
    
    def create_tool_button(self, parent, tool_name, tool_class, needs_app=False):
        """Create a simple button for a tool in the sidebar"""
        # Create the tool frame based on class type and needs_app flag
        if needs_app:
            # Classes that need the app reference
            tool_instance = tool_class(self.tool_container, self)
        else:
            # Classes that only need the parent container
            tool_instance = tool_class(self.tool_container)
        
        # Determine if the tool instance has a frame attribute
        if hasattr(tool_instance, 'frame'):
            tool_frame = tool_instance.frame
        else:
            # The tool instance is the frame itself
            tool_frame = tool_instance
        
        # Hide the tool frame initially
        tool_frame.pack_forget()
        
        # Store the tool frame
        self.tool_frames[tool_name] = {
            "frame": tool_frame,
            "instance": tool_instance,
            "name": tool_name
        }
        
        # Create a simple button for the tool
        tool_button = ttk.Button(
            parent,
            text=tool_name,
            command=lambda tn=tool_name: self.show_tool(tn)
        )
        tool_button.pack(fill=tk.X, padx=5, pady=2)
        
        # Store the button reference
        self.tool_frames[tool_name]["button"] = tool_button
    
    def show_tool(self, tool_name):
        """Show the selected tool and hide others"""
        # Hide current active tool if any
        if self.active_tool:
            self.tool_frames[self.active_tool]["frame"].pack_forget()
        
        # Show selected tool
        tool_frame = self.tool_frames[tool_name]["frame"]
        tool_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Update active tool
        self.active_tool = tool_name
        
        # Update status
        self.update_status(f"Selected: {tool_name}")
    
    def update_status(self, message):
        """Update the status bar with a message"""
        self.status_label.config(text=message)

    def show_about(self):
        messagebox.showinfo("About Image Toolbox", "Simple Image Toolbox Application")

    def quit_app(self, event=None):
        self.root.destroy()

    def run(self):
        self.root.mainloop()


if __name__ == "__main__":
    root = ttk.Window()
    app = ImageToolboxApp(root)
    app.run()
