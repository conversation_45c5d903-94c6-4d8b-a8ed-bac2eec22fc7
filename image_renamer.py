import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import threading

class ImageRenamer:
    def __init__(self, parent):
        self.frame = ttk.Frame(parent, padding=20)
        
        # Style configuration
        style = ttk.Style()
        style.configure("TButton", padding=12, font=('Arial', 12))  # Increased font size
        style.configure("TLabel", font=('Arial', 12), foreground="#333")  # Larger font
        style.configure("TEntry", padding=8, font=('Arial', 12))      # Larger font


        ttk.Label(self.frame, text="Select folder to rename images:", style="TLabel").pack(pady=(0,8), padx=20, anchor='w')
        
        select_button = ttk.Button(self.frame, text="Browse Folders", command=self.select_rename_folder, style="TButton")
        select_button.pack(pady=(0, 20), padx=20, anchor='w')
        
        self.folder_path_label = ttk.Label(self.frame, text="No folder selected", style="TLabel")
        self.folder_path_label.pack(pady=(0,20), padx=20, anchor='w')

        
        ttk.Label(self.frame, text="Rename Prefix:", style="TLabel").pack(pady=(0,8), padx=20, anchor='w')
        self.prefix_entry = ttk.Entry(self.frame, style="TEntry")
        self.prefix_entry.pack(pady=(0, 20), padx=20, fill='x')


        rename_button = ttk.Button(self.frame, text="Rename Images", command=self.rename_images_wrapper, style="TButton")
        rename_button.pack(pady=(20,25), padx=20, anchor='w')

        
        self.rename_status_label = ttk.Label(self.frame, text="", style="TLabel")
        self.rename_status_label.pack(pady=(0, 20), padx=20, anchor='w')
        
        self.rename_folder_path = ""

    def select_rename_folder(self):
        self.rename_folder_path = filedialog.askdirectory(title="Select Folder with Images")
        if self.rename_folder_path:
            self.folder_path_label.config(text=self.rename_folder_path)
        else:
            self.folder_path_label.config(text="No folder selected")
        

    def rename_images_wrapper(self):
        prefix = self.prefix_entry.get().strip()
        if not self.rename_folder_path or not prefix:
            messagebox.showerror("Error", "Please select a folder and provide a prefix.")
            return
        self.rename_status_label.config(text="Renaming Images...")
        threading.Thread(target=self._rename_images_threaded, args=(self.rename_folder_path, prefix)).start()

    def _rename_images_threaded(self, folder_path, prefix):
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
        images = [file for file in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, file)) and any(file.lower().endswith(ext) for ext in image_extensions)]
        if not images:
            messagebox.showwarning("Warning", "No images found in the selected folder.")
            self.rename_status_label.config(text="No images found.")
            return
        total_images = len(images)
        for idx, image_name in enumerate(images, start=1):
            old_path = os.path.join(folder_path, image_name)
            new_name = f"{prefix}_{idx:03d}{os.path.splitext(image_name)[1]}"
            new_path = os.path.join(folder_path, new_name)
            try:
                os.rename(old_path, new_path)
                self.rename_status_label.config(text=f"Renamed {image_name} to {new_name} ({idx}/{total_images})")
            except Exception as e:
                 self.rename_status_label.config(text=f"Error renaming {image_name}: {e} ({idx}/{total_images})")
        self.rename_status_label.config(text="Image renaming completed!")
        messagebox.showinfo("Success", "Image renaming completed!")


if __name__ == '__main__':
    root = tk.Tk()
    root.title("Image Renamer Tool")
    root.geometry("700x450")  # Increased size
    
    notebook = ttk.Notebook(root)
    notebook.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)
    
    image_renamer = ImageRenamer(notebook)
    root.mainloop()