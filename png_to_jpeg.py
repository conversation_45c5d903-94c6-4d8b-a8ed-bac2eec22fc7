import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, UnidentifiedImageError
import os
import threading
import concurrent.futures
from typing import Optional, Tuple, List
import logging
from enum import Enum
import time
import glob
import queue

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ColorMode(Enum):
    """Enumeration for color modes."""
    RGB = "RGB"
    GRAYSCALE = "Grayscale"


class PNGtoJPEGConverter:
    """
    A class for converting images from various formats to JPEG with flexible options.
    """
    def __init__(self, parent: ttk.Notebook):
        """
        Initializes the converter UI.

        Args:
            parent (ttk.Notebook): The parent notebook widget.
        """
        self.frame = ttk.Frame(parent, padding=15)
        
        # Styling for consistency
        style = ttk.Style()
        style.configure("TButton", padding=10)
        style.configure("TLabel", padding=5)
        style.configure("Header.TLabel", font=("Arial", 12, "bold"))
        
        # Main title
        ttk.Label(self.frame, text="Convert Images to JPEG", style="Header.TLabel").grid(
            row=0, column=0, columnspan=3, pady=(0, 15), sticky=tk.W)

        # Directory Selection Group
        dir_group = ttk.LabelFrame(self.frame, text="Directory Settings", padding=10)
        dir_group.grid(row=1, column=0, columnspan=3, padx=5, pady=5, sticky=tk.NSEW)
        
        # Input Directory
        ttk.Label(dir_group, text="Input Directory:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.input_dir_entry = ttk.Entry(dir_group, width=40)
        self.input_dir_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(dir_group, text="Browse", command=self._select_input_directory).grid(row=0, column=2, padx=5, pady=5)
        
        # Output Directory
        ttk.Label(dir_group, text="Output Directory:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.output_dir_entry = ttk.Entry(dir_group, width=40)
        self.output_dir_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(dir_group, text="Browse", command=self._select_output_directory).grid(row=1, column=2, padx=5, pady=5)
        
        # File Types
        ttk.Label(dir_group, text="File Types:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.file_types_entry = ttk.Entry(dir_group, width=40)
        self.file_types_entry.insert(0, ".png,.jpg,.jpeg,.bmp,.tiff")
        self.file_types_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Label(dir_group, text="(comma separated)").grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)
        
        # Image Settings Group
        img_group = ttk.LabelFrame(self.frame, text="Image Settings", padding=10)
        img_group.grid(row=2, column=0, columnspan=3, padx=5, pady=5, sticky=tk.NSEW)
        
        # Quality
        ttk.Label(img_group, text="Quality (1-100):").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.quality_entry = ttk.Entry(img_group, width=10)
        self.quality_entry.insert(0, "85")  # Better default quality
        self.quality_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Width and Height in the same row
        ttk.Label(img_group, text="Resize:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        
        size_frame = ttk.Frame(img_group)
        size_frame.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky=tk.W)
        
        ttk.Label(size_frame, text="Width:").pack(side=tk.LEFT, padx=(0, 5))
        self.width_entry = ttk.Entry(size_frame, width=8)
        self.width_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(size_frame, text="Height:").pack(side=tk.LEFT, padx=(0, 5))
        self.height_entry = ttk.Entry(size_frame, width=8)
        self.height_entry.pack(side=tk.LEFT)
        
        ttk.Label(size_frame, text="(pixels, optional)").pack(side=tk.LEFT, padx=(10, 0))
        
        # Color Mode
        ttk.Label(img_group, text="Color Mode:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.color_mode_combobox = ttk.Combobox(img_group, values=[mode.value for mode in ColorMode], state="readonly", width=12)
        self.color_mode_combobox.set(ColorMode.RGB.value)
        self.color_mode_combobox.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Conversion Options Group
        conv_group = ttk.LabelFrame(self.frame, text="Conversion Options", padding=10)
        conv_group.grid(row=3, column=0, columnspan=3, padx=5, pady=5, sticky=tk.NSEW)
        
        # Checkboxes for options
        options_frame = ttk.Frame(conv_group)
        options_frame.grid(row=0, column=0, columnspan=2, padx=5, pady=5, sticky=tk.W)
        
        # Overwrite Checkbox
        self.overwrite_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Overwrite Existing Files", variable=self.overwrite_var).grid(row=0, column=0, padx=5, pady=2, sticky=tk.W)
        
        # Remove Original Checkbox
        self.remove_original_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="Remove Original Files", variable=self.remove_original_var).grid(row=1, column=0, padx=5, pady=2, sticky=tk.W)
        
        # Check for new files checkbox
        self.check_new_files_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="Check for New Files After Conversion", variable=self.check_new_files_var).grid(row=2, column=0, padx=5, pady=2, sticky=tk.W)
        
        # Worker Threads
        ttk.Label(conv_group, text="Worker Threads:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        threads_frame = ttk.Frame(conv_group)
        threads_frame.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        self.worker_threads_entry = ttk.Entry(threads_frame, width=5)
        self.worker_threads_entry.insert(0, str(min(os.cpu_count() or 1, 4)))
        self.worker_threads_entry.pack(side=tk.LEFT)
        
        ttk.Label(threads_frame, text="(1-16 recommended)").pack(side=tk.LEFT, padx=(5, 0))
        
        # Feedback Group
        feedback_group = ttk.LabelFrame(self.frame, text="Progress", padding=10)
        feedback_group.grid(row=4, column=0, columnspan=3, padx=5, pady=5, sticky=tk.NSEW)
        
        # Status Label
        self.status_label = ttk.Label(feedback_group, text="Ready to convert", foreground="blue")
        self.status_label.pack(pady=(0, 10), fill=tk.X)
        
        # Progress Bar
        self.progress_bar = ttk.Progressbar(feedback_group, orient="horizontal", length=300, mode="determinate")
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        self.progress_bar["value"] = 0
        
        # Convert Button
        self.convert_button = ttk.Button(feedback_group, text="Convert Images", command=self._start_conversion)
        self.convert_button.pack(pady=(0, 5), ipadx=10, ipady=5)
        
        # Configure grid
        self.frame.columnconfigure(0, weight=1)
        self.frame.columnconfigure(1, weight=3)
        self.frame.columnconfigure(2, weight=1)
        
        # Class variables
        self.processed_files = set()
        self.lock = threading.Lock()
        self.update_queue = queue.Queue()
        self.frame.after(100, self.process_gui_updates)

    def _select_input_directory(self):
        """Opens a dialog to select the input directory and updates the entry field."""
        input_dir = filedialog.askdirectory(title="Select Input Directory")
        self.input_dir_entry.delete(0, tk.END)
        self.input_dir_entry.insert(0, input_dir)

    def _select_output_directory(self):
        """Opens a dialog to select the output directory and updates the entry field."""
        output_dir = filedialog.askdirectory(title="Select Output Directory")
        self.output_dir_entry.delete(0, tk.END)
        self.output_dir_entry.insert(0, output_dir)

    def _start_conversion(self):
        """
        Validates user inputs and starts the conversion process in a separate thread.
        """
        # Disable the convert button to prevent multiple clicks
        self.convert_button.config(state=tk.DISABLED)
        
        input_dir = self.input_dir_entry.get()
        output_dir = self.output_dir_entry.get()
        quality = self.quality_entry.get()
        width = self.width_entry.get()
        height = self.height_entry.get()
        color_mode = self.color_mode_combobox.get()
        overwrite = self.overwrite_var.get()
        remove_original = self.remove_original_var.get()
        worker_threads = self.worker_threads_entry.get()
        file_types = self.file_types_entry.get()
        check_new_files = self.check_new_files_var.get()

        if not all([input_dir, output_dir, quality, file_types]):
            messagebox.showerror("Error", "Please fill in all required fields.")
            self.convert_button.config(state=tk.NORMAL)
            return

        try:
            quality = int(quality)
            if not 1 <= quality <= 100:
                raise ValueError("Quality must be between 1 and 100")
            width = int(width) if width else None
            height = int(height) if height else None
            worker_threads = min(int(worker_threads), 16)  # Add upper bound
            file_types = [ft.strip() for ft in file_types.split(',')]

            self.status_label.config(text="Converting images...", foreground="blue")
            self.progress_bar["value"] = 0
            
            # Start conversion in a separate thread
            threading.Thread(
                target=self._convert_images_threaded,
                args=(input_dir, output_dir, quality, width, height, color_mode, overwrite, remove_original, worker_threads, file_types, check_new_files)
            ).start()

        except ValueError as ve:
            messagebox.showerror("Error", str(ve))
            self.convert_button.config(state=tk.NORMAL)

    def _convert_images_threaded(self, input_dir: str, output_dir: str, quality: int, width: Optional[int], height: Optional[int],
            color_mode: str, overwrite: bool, remove_original: bool, worker_threads: int, file_types: List[str], check_new_files: bool):
        """
         Converts image files to JPEG using a thread pool for concurrency.

        Args:
             input_dir (str): Input directory containing image files.
             output_dir (str): Output directory for JPEG files.
             quality (int): JPEG quality (1-100).
             width (Optional[int]): Desired width for resizing.
             height (Optional[int]): Desired height for resizing.
             color_mode (str): Color mode ("RGB" or "Grayscale").
             overwrite (bool): Whether to overwrite existing files.
             remove_original (bool): Whether to remove the original file.
             worker_threads (int): Number of worker threads to use.
             file_types (List[str]): List of file extensions to include for processing
             check_new_files (bool) : If True, check for new files after conversion
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            def process_files():
                patterns = [os.path.join(input_dir, f"*{ft}") for ft in file_types]
                current_files = set()
                for pattern in patterns:
                    current_files.update(glob.glob(pattern, recursive=False))
                
                with self.lock:
                    new_files = [
                        f for f in current_files 
                        if f not in self.processed_files 
                        and not os.path.basename(f).startswith('.')  # Skip hidden files
                    ]
                    self.processed_files.update(new_files)
                
                total_files = len(new_files)
                if not total_files:
                    return 0

                converted_count = 0
                
                with concurrent.futures.ThreadPoolExecutor(max_workers=worker_threads) as executor:
                    futures = {
                        executor.submit(
                            self._convert_single_image,
                            input_dir, output_dir, file_name, quality, width, height, color_mode, overwrite, remove_original, index, total_files
                        ): file_name
                        for index, file_name in enumerate(new_files, start=1)
                    }

                    for future in concurrent.futures.as_completed(futures):
                        file_name = futures[future]
                        try:
                            result = future.result()
                            if result == "converted":
                                converted_count += 1
                            elif result.startswith("error"):
                                self.update_queue.put(("status", result.split(":", 1)[1], "red"))
                            self.update_queue.put(("progress", int((converted_count/total_files)*100) if total_files > 0 else 0))
                        except Exception as e:
                            self.update_queue.put(("status", f"Error processing {file_name}: {str(e)}", "red"))
                return converted_count

            converted_count = process_files()
            
            if check_new_files:
                while True:
                    time.sleep(5)
                    new_converted_count = process_files()
                    if new_converted_count > 0:
                        converted_count += new_converted_count
                        self.update_queue.put(("status", f"Found and converted {new_converted_count} new files. Total: {converted_count}", "blue"))
                    else:
                        self.update_queue.put(("status", "No new files detected. Stopping check.", "green"))
                        break
                    
            self.update_queue.put(("status", f"Conversion completed. Total files converted: {converted_count}", "green"))
            self.update_queue.put(("button_state", tk.NORMAL))
            
            if converted_count > 0:
                messagebox.showinfo("Success", f"Image conversion completed. {converted_count} file(s) converted.")
            else:
                messagebox.showinfo("Information", "No files were converted.")

            if remove_original and converted_count > 0:
                removed_count = 0
                for file_path in self.processed_files:
                    if os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                            removed_count += 1
                        except Exception as e:
                            logging.error(f"Error removing {file_path}: {str(e)}")
                
                self.update_queue.put(("status", f"Removed {removed_count} original files.", "blue"))

        except Exception as e:
            self.update_queue.put(("status", f"An error occurred: {str(e)}", "red"))
            self.update_queue.put(("button_state", tk.NORMAL))
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
            logging.error(f"An unexpected error occurred: {e}", exc_info=True)

    def _convert_single_image(self, input_dir: str, output_dir: str, file_name: str, quality: int, width: Optional[int],
            height: Optional[int], color_mode: str, overwrite: bool, remove_original: bool, index: int, total_files: int) -> str:
        """
        Converts a single image file to JPEG.

         Args:
            input_dir (str): The input directory.
            output_dir (str): The output directory.
            file_name (str): The name of the image file.
            quality (int): JPEG quality (1-100).
            width (Optional[int]): Desired width for resizing.
            height (Optional[int]): Desired height for resizing.
            color_mode (str): Color mode ("RGB" or "Grayscale").
            overwrite (bool): Whether to overwrite existing files.
            remove_original (bool): Whether to remove the original image file.
            index (int): The index of the current file being processed.
            total_files (int): The total number of files to convert.

         Returns:
             str: Status of the conversion ("converted", "skipped", or "error").
        """
        input_path = file_name  # file_name is already the full path
        base_name = os.path.basename(file_name)
        output_path = os.path.join(output_dir, os.path.splitext(base_name)[0] + '.jpeg')

        if os.path.exists(output_path) and not overwrite:
            self.update_queue.put(("status", f"Skipped {base_name} (already exists) ({index}/{total_files})", "blue"))
            return "skipped"
        try:
            image = Image.open(input_path)
            if width or height:
                new_size = (width if width else image.width, height if height else image.height)
                image = image.resize(new_size, Image.LANCZOS)

            if color_mode == ColorMode.GRAYSCALE.value:
                 image = image.convert("L")
            else:
                image = image.convert("RGB")

            if image.mode == 'P':
                image = image.convert('RGBA')

            image.save(output_path, "JPEG", quality=quality, optimize=True, progressive=True)
            self.update_queue.put(("status", f"Converted {base_name} ({index}/{total_files})", "blue"))

            return "converted"
        except FileNotFoundError:
             return f"error:File not found: {base_name} ({index}/{total_files})"
        except UnidentifiedImageError:
             return f"error:Cannot identify image file: {base_name} ({index}/{total_files})"
        except Exception as conversion_error:
             logging.error(f"Error converting {base_name}: {str(conversion_error)}", exc_info=True)
             return f"error:Error converting {base_name}: {str(conversion_error)} ({index}/{total_files})"

    def process_gui_updates(self):
        while not self.update_queue.empty():
            try:
                update_type, *data = self.update_queue.get()
                if update_type == "progress":
                    self.progress_bar["value"] = data[0]
                elif update_type == "status":
                    self.status_label.config(text=data[0], foreground=data[1] if len(data) > 1 else "blue")
                elif update_type == "button_state":
                    self.convert_button.config(state=data[0])
            except Exception as e:
                logging.error(f"Error processing GUI update: {str(e)}")
        
        self.frame.after(100, self.process_gui_updates)

if __name__ == '__main__':
    # This is for testing the module in isolation
    root = tk.Tk()
    root.title("Image Converter")
    notebook = ttk.Notebook(root)
    notebook.pack(expand=True, fill=tk.BOTH)
    image_converter = PNGtoJPEGConverter(notebook)
    root.mainloop()