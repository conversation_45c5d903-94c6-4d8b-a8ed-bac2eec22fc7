import tkinter as tk
from tkinter import filedialog
from tkinter import messagebox
import os
import shutil
import threading
import time
import logging
from PIL import Image

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

class App:
    def __init__(self, master):
        self.master = master

        self.source_dir = ""
        self.destination_dir = ""
        self.running = False
        self.image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp']
        self.interval = 600  # 10 minutes in seconds

        # Create UI elements
        self.source_entry = tk.Entry(master, width=50)
        self.dest_entry = tk.Entry(master, width=50)
        
        # Add interval adjustment
        self.interval_entry = tk.Entry(master, width=5)
        self.interval_entry.insert(0, str(self.interval // 60))  # Show minutes instead of seconds
        
        self.start_button = tk.Button(master, text="Start Processing", command=self.toggle_processing)
        self.status_label = tk.Label(master, text="Waiting...", fg='gray')

    def browse_source(self):
        self.source_dir = filedialog.askdirectory()
        self.source_entry.delete(0, tk.END)
        self.source_entry.insert(0, self.source_dir)

    def browse_dest(self):
        self.destination_dir = filedialog.askdirectory()
        self.dest_entry.delete(0, tk.END)
        self.dest_entry.insert(0, self.destination_dir)

    def toggle_processing(self):
        if not self.running:
            try:
                interval_minutes = int(self.interval_entry.get())
                if interval_minutes <= 0:
                    messagebox.showerror("Error", "Interval must be greater than 0 minutes.")
                    return
                self.interval = interval_minutes * 60  # Convert minutes to seconds
            except ValueError:
                messagebox.showerror("Error", "Invalid interval. Please enter a valid number of minutes.")
                return
                
            if not self.source_dir or not self.destination_dir:
                 messagebox.showerror("Error", "Please select both a source and destination folder.")
                 return
            if not os.path.isdir(self.source_dir):
                messagebox.showerror("Error", "Source folder not valid.")
                return
            if not os.path.isdir(self.destination_dir):
                 messagebox.showerror("Error", "Destination folder not valid.")
                 return
            self.start_processing()
        else:
            self.stop_processing()

    def start_processing(self):
        self.running = True
        self.start_button.config(text="Stop Processing")
        self.status_label.config(text=f"Processing Scheduled (Every {self.interval//60} minutes)...", fg='blue')
        self.thread = threading.Thread(target=self.scheduled_move)
        self.thread.daemon = True
        self.thread.start()
        logging.info(f"Scheduled processing started with {self.interval//60} minute interval.")

    def stop_processing(self):
        self.running = False
        self.start_button.config(text="Start Processing")
        self.status_label.config(text="Stopped", fg='gray')
        logging.info("Scheduled processing stopped.")

    def scheduled_move(self):
         while self.running:
            logging.info("Scheduled processing tick.")
            self.process_images()
            time.sleep(self.interval)

    def process_images(self):
        if not self.source_dir or not self.destination_dir:
            return

        files_to_move = []
        error_files = []
        
        # Collect all valid image files with their modification times
        for filename in os.listdir(self.source_dir):
            file_path = os.path.join(self.source_dir, filename)
            if os.path.isfile(file_path) and any(filename.lower().endswith(ext) for ext in self.image_extensions):
                try:
                    with Image.open(file_path) as img:
                        files_to_move.append((file_path, os.path.getmtime(file_path)))
                except Exception as e:
                    error_files.append(f"File {filename} error: {e}")
                    logging.error(f"Error checking {filename}: {e}")

        if not files_to_move:
            if error_files:
                self.status_label.config(text=f"Found errors: {len(error_files)}. No image found to be moved.", fg='red')
                logging.info(f"No valid images to move after detection. Number of images which could not be checked : {len(error_files)}")
            else:
                logging.info(f"No valid images to move")
            return

        # Sort files by modification time (newest first)
        files_to_move.sort(key=lambda item: item[1], reverse=True)
        
        # Keep the 10 most recent files, move the rest
        files_to_keep = files_to_move[:10]
        files_to_move = files_to_move[10:]
        
        moved_count = 0
        move_errors = []
        
        # Move all files except the 10 most recent ones
        for file_path, _ in files_to_move:
            filename = os.path.basename(file_path)
            try:
                new_path = os.path.join(self.destination_dir, filename)
                shutil.move(file_path, new_path)
                moved_count += 1
            except Exception as e:
                move_errors.append(f"Failed to move {filename}: {e}")
                logging.error(f"Error moving {filename}: {e}")

        if moved_count == 0:
            if move_errors:
                self.status_label.config(text=f"No images moved, encountered errors: {len(move_errors)}", fg='red')
            else:
                self.status_label.config(text=f"No images to move. Keeping {len(files_to_keep)} recent images.", fg='gray')
        else:
            self.status_label.config(text=f"Moved {moved_count} image(s), kept {len(files_to_keep)} recent images. {len(move_errors)} errors occurred.", fg='green' if len(move_errors) == 0 else 'red')
            logging.info(f"Moved {moved_count} image(s), kept {len(files_to_keep)} recent images. {len(move_errors)} move errors in this interval.")
       
def main():
        root = tk.Tk()
        root.title("Image Mover (Keep 10 Recent)")
        app = App(root)

        tk.Label(root, text="Source Folder:").grid(row=0, column=0, sticky="e")
        app.source_entry.grid(row=0, column=1, padx=5, pady=5)
        tk.Button(root, text="Browse", command=app.browse_source).grid(row=0, column=2, padx=5, pady=5)

        tk.Label(root, text="Destination Folder (SSD):").grid(row=1, column=0, sticky="e")
        app.dest_entry.grid(row=1, column=1, padx=5, pady=5)
        tk.Button(root, text="Browse", command=app.browse_dest).grid(row=1, column=2, padx=5, pady=5)

        tk.Label(root, text="Check Interval (minutes):").grid(row=2, column=0, sticky="e")
        app.interval_entry.grid(row=2, column=1, sticky="w", padx=5, pady=5)

        app.start_button.grid(row=3, column=0, columnspan=3, pady=10)
        app.status_label.grid(row=4, column=0, columnspan=3)
        root.mainloop()


if __name__ == '__main__':
        main()