import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import hashlib
from PIL import Image
import threading

class ImageCleanerFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=15)
        self.app = app
        self.create_widgets()

    def create_widgets(self):
        # Create a responsive grid layout
        self.columnconfigure(0, weight=1)
        self.rowconfigure(3, weight=1)  # Make the status area expandable
        
        # Header section with title and description
        header_frame = ttk.Frame(self)
        header_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=(0, 15))
        header_frame.columnconfigure(0, weight=1)
        
        # Title with icon
        title_frame = ttk.Frame(header_frame)
        title_frame.grid(row=0, column=0, sticky="w")
        
        title_icon = ttk.Label(
            title_frame,
            text="🧹",  # Broom emoji
            font=("Helvetica", 24)
        )
        title_icon.pack(side=tk.LEFT, padx=(0, 10))
        
        title_text = ttk.Label(
            title_frame,
            text="Image Folder Cleaner",
            font=("Helvetica", 18, "bold"),
            bootstyle="info"
        )
        title_text.pack(side=tk.LEFT)
        
        # Description text
        description_frame = ttk.Frame(header_frame)
        description_frame.grid(row=1, column=0, sticky="ew", pady=(10, 0))
        
        description_label = ttk.Label(
            description_frame,
            text="This tool helps you clean up your image folders by removing non-JPG files and identifying duplicate images based on content.",
            wraplength=800,
            justify="left",
            bootstyle="secondary"
        )
        description_label.pack(fill=tk.X)
        
        # Warning message
        warning_frame = ttk.Frame(header_frame, bootstyle="danger")
        warning_frame.grid(row=2, column=0, sticky="ew", pady=(15, 0))
        
        warning_icon = ttk.Label(
            warning_frame,
            text="⚠️",
            font=("Helvetica", 16)
        )
        warning_icon.pack(side=tk.LEFT, padx=(0, 10))
        
        warning_label = ttk.Label(
            warning_frame,
            text="WARNING: This will permanently delete files from the selected folder!",
            bootstyle="danger",
            font=("Helvetica", 10, "bold")
        )
        warning_label.pack(side=tk.LEFT)
        
        # Main content area with card styling
        content_frame = ttk.Frame(self, bootstyle="default")
        content_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=15)
        content_frame.columnconfigure(0, weight=1)
        
        # Folder selection with modern styling
        folder_frame = ttk.LabelFrame(content_frame, text="Select Folder", padding=15)
        folder_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))
        folder_frame.columnconfigure(1, weight=1)
        
        folder_icon = ttk.Label(
            folder_frame,
            text="📁",
            font=("Helvetica", 14)
        )
        folder_icon.grid(row=0, column=0, padx=(0, 10))
        
        self.folder_var = tk.StringVar()
        folder_entry = ttk.Entry(
            folder_frame,
            textvariable=self.folder_var,
            bootstyle="info",
            font=("Helvetica", 10)
        )
        folder_entry.grid(row=0, column=1, sticky="ew", padx=(0, 10))
        
        browse_button = ttk.Button(
            folder_frame,
            text="Browse",
            command=self.browse_folder,
            bootstyle="info",
            width=10
        )
        browse_button.grid(row=0, column=2)
        
        # Options section with toggle switches
        options_frame = ttk.LabelFrame(content_frame, text="Cleaning Options", padding=15)
        options_frame.grid(row=1, column=0, sticky="ew")
        options_frame.columnconfigure(0, weight=1)
        
        # Option 1: Remove non-JPG files
        option1_frame = ttk.Frame(options_frame)
        option1_frame.grid(row=0, column=0, sticky="ew", pady=5)
        option1_frame.columnconfigure(1, weight=1)
        
        self.remove_non_jpg_var = tk.BooleanVar(value=True)
        remove_non_jpg_check = ttk.Checkbutton(
            option1_frame,
            text="Remove non-JPG files",
            variable=self.remove_non_jpg_var,
            bootstyle="round-toggle-info"
        )
        remove_non_jpg_check.grid(row=0, column=0, sticky="w")
        
        option1_help = ttk.Label(
            option1_frame,
            text="Removes all files that are not JPG format (PNG, GIF, BMP, etc.)",
            bootstyle="secondary",
            font=("Helvetica", 9)
        )
        option1_help.grid(row=0, column=1, sticky="e", padx=10)
        
        # Option 2: Remove duplicate images
        option2_frame = ttk.Frame(options_frame)
        option2_frame.grid(row=1, column=0, sticky="ew", pady=5)
        option2_frame.columnconfigure(1, weight=1)
        
        self.remove_duplicates_var = tk.BooleanVar(value=True)
        remove_duplicates_check = ttk.Checkbutton(
            option2_frame,
            text="Remove duplicate images",
            variable=self.remove_duplicates_var,
            bootstyle="round-toggle-info"
        )
        remove_duplicates_check.grid(row=0, column=0, sticky="w")
        
        option2_help = ttk.Label(
            option2_frame,
            text="Identifies and removes duplicate images based on content hash",
            bootstyle="secondary",
            font=("Helvetica", 9)
        )
        option2_help.grid(row=0, column=1, sticky="e", padx=10)
        
        # Action buttons with modern styling
        buttons_frame = ttk.Frame(self)
        buttons_frame.grid(row=2, column=0, sticky="ew", padx=5, pady=15)
        
        # Center the buttons
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(2, weight=1)
        
        # Start button
        start_button = ttk.Button(
            buttons_frame,
            text="Start Cleaning",
            command=self.start_cleaning,
            bootstyle="success",
            width=20
        )
        start_button.grid(row=0, column=1, padx=5)
        
        # Status section with progress
        status_frame = ttk.LabelFrame(self, text="Status", padding=15)
        status_frame.grid(row=3, column=0, sticky="nsew", padx=5, pady=(0, 5))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(1, weight=1)
        
        # Status label
        self.status_label = ttk.Label(
            status_frame,
            text="Ready to clean",
            bootstyle="secondary"
        )
        self.status_label.grid(row=0, column=0, sticky="w", pady=(0, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            status_frame,
            variable=self.progress_var,
            bootstyle="success-striped",
            mode="determinate",
            length=100
        )
        self.progress_bar.grid(row=1, column=0, sticky="ew")
        
        # Results display area with scrollbar
        results_frame = ttk.Frame(status_frame)
        results_frame.grid(row=2, column=0, sticky="nsew", pady=(10, 0))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Create scrollable text widget for results
        self.results_text = tk.Text(
            results_frame,
            height=8,
            width=50,
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg="#f8f9fa",
            fg="#212529"
        )
        self.results_text.grid(row=0, column=0, sticky="nsew")
        self.results_text.config(state=tk.DISABLED)
        
        # Add scrollbar
        results_scrollbar = ttk.Scrollbar(
            results_frame,
            orient="vertical",
            command=self.results_text.yview
        )
        results_scrollbar.grid(row=0, column=1, sticky="ns")
        self.results_text.config(yscrollcommand=results_scrollbar.set)

    def browse_folder(self):
        """Open a folder dialog to select the image folder"""
        folder_path = filedialog.askdirectory(title="Select Image Folder")
        if folder_path:
            self.folder_var.set(folder_path)
            self.update_status(f"Selected folder: {folder_path}")
    
    def update_status(self, message):
        """Update the status label with a message"""
        self.status_label.config(text=message)
        
        # Also update the app status if available
        if hasattr(self.app, 'update_status'):
            self.app.update_status(message)
        
        # Add message to results text
        self.results_text.config(state=tk.NORMAL)
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.results_text.config(state=tk.DISABLED)
    
    def update_progress(self, progress, message=None):
        """Update the progress bar and optionally the status message"""
        self.progress_var.set(progress)
        
        if message:
            self.update_status(message)
        
        # Update the app progress if available
        if hasattr(self.app, 'show_progress'):
            self.app.show_progress(progress, message)
    
    def start_cleaning(self):
        """Start the cleaning process in a separate thread"""
        folder_path = self.folder_var.get()
        
        if not folder_path:
            messagebox.showerror("Error", "Please select a folder first")
            return
        
        if not os.path.isdir(folder_path):
            messagebox.showerror("Error", "Selected path is not a valid directory")
            return
        
        # Confirm before proceeding
        if not messagebox.askyesno("Confirm", 
                                  "This will permanently delete files from the selected folder. Continue?",
                                  icon="warning"):
            return
        
        # Reset progress
        self.progress_var.set(0)
        self.update_status("Starting cleaning process...")
        
        # Clear results text
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete(1.0, tk.END)
        self.results_text.config(state=tk.DISABLED)
        
        # Start cleaning in a separate thread
        cleaning_thread = threading.Thread(
            target=self.run_cleaning,
            args=(folder_path,)
        )
        cleaning_thread.daemon = True
        cleaning_thread.start()
    
    def get_image_hash(self, image_path):
        """Generate a perceptual hash for an image to identify duplicates"""
        try:
            with Image.open(image_path) as img:
                # Resize to small square for comparison
                img = img.resize((8, 8), Image.LANCZOS).convert('L')
                # Calculate average pixel value
                pixels = list(img.getdata())
                avg = sum(pixels) / len(pixels)
                # Create hash based on whether pixel is above average
                bits = ''.join('1' if pixel > avg else '0' for pixel in pixels)
                # Convert binary string to hexadecimal
                return hashlib.md5(bits.encode()).hexdigest()
        except Exception as e:
            self.update_status(f"Error hashing image {image_path}: {str(e)}")
            return None
    
    def run_cleaning(self, folder_path):
        """Run the cleaning process"""
        try:
            # Get all files in the folder
            all_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) 
                        if os.path.isfile(os.path.join(folder_path, f))]
            
            total_files = len(all_files)
            if total_files == 0:
                self.update_status("No files found in the selected folder")
                self.update_progress(100)
                return
            
            self.update_status(f"Found {total_files} files in the folder")
            
            # Step 1: Remove non-JPG files if option is selected
            removed_non_jpg = 0
            if self.remove_non_jpg_var.get():
                self.update_status("Removing non-JPG files...")
                jpg_files = []
                
                for i, file_path in enumerate(all_files):
                    # Update progress
                    progress = (i / total_files) * 50  # First 50% of progress
                    self.update_progress(progress)
                    
                    # Check if file is JPG
                    file_ext = os.path.splitext(file_path)[1].lower()
                    if file_ext in ['.jpg', '.jpeg']:
                        jpg_files.append(file_path)
                    else:
                        try:
                            os.remove(file_path)
                            removed_non_jpg += 1
                            self.update_status(f"Removed non-JPG file: {os.path.basename(file_path)}")
                        except Exception as e:
                            self.update_status(f"Error removing file {file_path}: {str(e)}")
                
                self.update_status(f"Removed {removed_non_jpg} non-JPG files")
                all_files = jpg_files
            
            # Step 2: Remove duplicate images if option is selected
            removed_duplicates = 0
            if self.remove_duplicates_var.get() and all_files:
                self.update_status("Identifying duplicate images...")
                
                # Dictionary to store image hashes
                image_hashes = {}
                unique_files = []
                
                for i, file_path in enumerate(all_files):
                    # Update progress
                    progress = 50 + (i / len(all_files)) * 50  # Second 50% of progress
                    self.update_progress(progress)
                    
                    # Get image hash
                    img_hash = self.get_image_hash(file_path)
                    if img_hash:
                        if img_hash in image_hashes:
                            # This is a duplicate
                            try:
                                os.remove(file_path)
                                removed_duplicates += 1
                                self.update_status(f"Removed duplicate image: {os.path.basename(file_path)}")
                            except Exception as e:
                                self.update_status(f"Error removing duplicate {file_path}: {str(e)}")
                        else:
                            # This is a unique image
                            image_hashes[img_hash] = file_path
                            unique_files.append(file_path)
                
                self.update_status(f"Removed {removed_duplicates} duplicate images")
                all_files = unique_files
            
            # Complete
            self.update_progress(100)
            self.update_status(f"Cleaning complete. Removed {removed_non_jpg} non-JPG files and {removed_duplicates} duplicates.")
            self.update_status(f"Remaining files: {len(all_files)}")
            
            # Hide app progress if available
            if hasattr(self.app, 'hide_progress'):
                self.app.hide_progress()
                
        except Exception as e:
            self.update_status(f"Error during cleaning: {str(e)}")
            if hasattr(self.app, 'hide_progress'):
                self.app.hide_progress() 