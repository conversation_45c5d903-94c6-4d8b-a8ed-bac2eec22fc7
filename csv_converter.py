import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import pandas as pd
import os
import math
import sys
import threading

class CSVConverter(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent, padding="20")
        self.parent = parent
        self.processing = False
        self.create_widgets()
        
    def create_widgets(self):
        # Main container with modern card-like appearance
        main_frame = ttk.Frame(self, style='Card.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header section
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        header_label = ttk.Label(
            header_frame,
            text="CSV Converter",
            font=('Inter', 16, 'bold'),
            foreground='#2D3748'
        )
        header_label.pack(side=tk.LEFT)
        
        # Status badge
        self.status_badge = ttk.Label(
            header_frame,
            text="Ready",
            style='Success.TLabel',
            padding=(10, 5)
        )
        self.status_badge.pack(side=tk.RIGHT)
        
        # Input section
        input_frame = ttk.LabelFrame(
            main_frame,
            text="Input Settings",
            padding=15,
            style='Card.TLabelframe'
        )
        input_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # File selection
        file_frame = ttk.Frame(input_frame)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_entry = ttk.Entry(
            file_frame,
            font=('Inter', 10)
        )
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        browse_btn = ttk.Button(
            file_frame,
            text="Browse",
            command=self.browse_file,
            style='Outline.TButton'
        )
        browse_btn.pack(side=tk.RIGHT)
        
        # Delimiter selection
        delimiter_frame = ttk.Frame(input_frame)
        delimiter_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(
            delimiter_frame,
            text="Delimiter:",
            font=('Inter', 10)
        ).pack(side=tk.LEFT)
        
        self.delimiter_var = tk.StringVar(value=',')
        delimiters = [',', ';', '|', '\t']
        
        for delimiter in delimiters:
            display_text = 'Tab' if delimiter == '\t' else delimiter
            rb = ttk.Radiobutton(
                delimiter_frame,
                text=display_text,
                value=delimiter,
                variable=self.delimiter_var,
                style='Toolbutton'
            )
            rb.pack(side=tk.LEFT, padx=5)
        
        # Output section
        output_frame = ttk.LabelFrame(
            main_frame,
            text="Output Settings",
            padding=15,
            style='Card.TLabelframe'
        )
        output_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Output format selection
        format_frame = ttk.Frame(output_frame)
        format_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(
            format_frame,
            text="Output Format:",
            font=('Inter', 10)
        ).pack(side=tk.LEFT)
        
        self.output_format = tk.StringVar(value='excel')
        formats = [
            ('Excel', 'excel'),
            ('CSV', 'csv'),
            ('JSON', 'json'),
            ('HTML', 'html')
        ]
        
        format_buttons_frame = ttk.Frame(format_frame)
        format_buttons_frame.pack(side=tk.LEFT, padx=10)
        
        for text, value in formats:
            ttk.Radiobutton(
                format_buttons_frame,
                text=text,
                value=value,
                variable=self.output_format,
                style='Toolbutton'
            ).pack(side=tk.LEFT, padx=2)
        
        # Action buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, padx=20, pady=20)
        
        self.convert_btn = ttk.Button(
            button_frame,
            text="Convert",
            command=self.start_conversion,
            style='Primary.TButton',
            width=15
        )
        self.convert_btn.pack(side=tk.RIGHT, padx=5)
        
        self.cancel_btn = ttk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel_conversion,
            style='Secondary.TButton',
            width=15,
            state='disabled'
        )
        self.cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # Progress section
        self.progress_frame = ttk.Frame(main_frame)
        self.progress_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            mode='determinate',
            style='Striped.Horizontal.TProgressbar'
        )
        self.progress_bar.pack(fill=tk.X)
        
        self.progress_label = ttk.Label(
            self.progress_frame,
            text="",
            font=('Inter', 9),
            foreground='#718096'
        )
        self.progress_label.pack(pady=(5, 0))
        
        # Initially hide progress elements
        self.progress_frame.pack_forget()
        
    def browse_file(self):
        """Open file dialog to select input CSV file"""
        filename = filedialog.askopenfilename(
            title="Select CSV file",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.file_entry.delete(0, tk.END)
            self.file_entry.insert(0, filename)
            
    def update_status(self, status, is_error=False):
        """Update the status badge with new status"""
        self.status_badge.configure(
            text=status,
            style='Danger.TLabel' if is_error else 'Success.TLabel'
        )
        
    def start_conversion(self):
        """Start the conversion process in a separate thread"""
        if not self.file_entry.get():
            self.update_status("No file selected", True)
            return
            
        self.processing = True
        self.convert_btn.configure(state='disabled')
        self.cancel_btn.configure(state='normal')
        self.progress_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        self.progress_bar['value'] = 0
        
        # Start conversion in a separate thread
        self.conversion_thread = threading.Thread(target=self.convert_file)
        self.conversion_thread.start()
        
    def cancel_conversion(self):
        """Cancel the ongoing conversion process"""
        self.processing = False
        self.update_status("Cancelled")
        self.progress_label.configure(text="Conversion cancelled")
        self.convert_btn.configure(state='normal')
        self.cancel_btn.configure(state='disabled')
        
    def convert_file(self):
        """Convert the CSV file to the selected output format"""
        try:
            input_file = self.file_entry.get()
            delimiter = self.delimiter_var.get()
            output_format = self.output_format.get()
            
            # Read CSV file
            df = pd.read_csv(input_file, delimiter=delimiter)
            
            # Get output filename
            output_file = os.path.splitext(input_file)[0]
            
            # Convert to selected format
            if output_format == 'excel':
                output_file += '.xlsx'
                df.to_excel(output_file, index=False)
            elif output_format == 'json':
                output_file += '.json'
                df.to_json(output_file, orient='records')
            elif output_format == 'html':
                output_file += '.html'
                df.to_html(output_file, index=False)
            else:  # CSV
                output_file += '_converted.csv'
                df.to_csv(output_file, index=False)
                
            self.update_status("Conversion Complete")
            self.progress_bar['value'] = 100
            self.progress_label.configure(text=f"Saved as: {os.path.basename(output_file)}")
            
        except Exception as e:
            self.update_status("Error", True)
            self.progress_label.configure(text=str(e))
            
        finally:
            self.processing = False
            self.convert_btn.configure(state='normal')
            self.cancel_btn.configure(state='disabled')

if __name__ == '__main__':
    root = tk.Tk()
    root.title("CSV Converter App")
    notebook = ttk.Notebook(root)
    notebook.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)

    # Window Size
    root.geometry("600x500")  # Increased size for new controls
    root.minsize(500, 450)
    csv_converter = CSVConverter(notebook)
    root.mainloop()