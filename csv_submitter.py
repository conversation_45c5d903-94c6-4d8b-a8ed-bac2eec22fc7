import os
import time
import random
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import requests
from twilio.rest import Client
import threading
import logging

class CSVSubmitterFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        
        # Twilio credentials
        self.account_sid = '**********************************'
        self.auth_token = '884f6841156a0309ce9e49358101c0e6'
        self.flow_sid = "FWe80b98a9b5f3667cc5d1e26e02d8d4ec"
        self.to_phone_number = "+************"
        self.from_twilio_number = "+***********"
        
        # 2Captcha API Key
        self.API_KEY = '3cdfb9274f49e32c6058842434552f9c'
        
        # Initialize variables
        self.driver = None
        self.csv_data = None
        self.current_row = 0
        self.automation_active = False
        self.paused = False
        self.aspect_ratios = ["16:9", "4:3", "1:1", "21:9", "3:2"]
        self.automation_timeout = 5000  # Default timeout
        self.rows_to_process = 0
        self.call_count = 0  # Counter for calls during error
        self.loaded_csv_path = None  # Store the loaded csv path
        
        # Initialize Tkinter variables
        self.delete_after_sent = tk.BooleanVar(value=True)
        self.use_ar_feature = tk.BooleanVar(value=True)
        self.selected_encoding = tk.StringVar(value="utf-8")
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create and setup all UI widgets"""
        # Main container Frame with modern styling
        main_frame = ttk.LabelFrame(self, text="CSV Submitter Tool", padding=15, bootstyle="info")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control Frame
        control_frame = ttk.LabelFrame(main_frame, text="Controls", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Grid layout for controls
        self.load_button = ttk.Button(control_frame, text="Load CSV", command=self.load_csv, bootstyle="primary")
        self.load_button.grid(row=0, column=0, pady=5, padx=5, sticky="ew")
        
        self.open_page_button = ttk.Button(control_frame, text="Open Web Page", command=self.open_web_page, bootstyle="primary")
        self.open_page_button.grid(row=0, column=1, pady=5, padx=5, sticky="ew")
        
        self.submit_button = ttk.Button(control_frame, text="Submit Next Row", command=self.submit_next_row, state=tk.DISABLED, bootstyle="secondary")
        self.submit_button.grid(row=1, column=0, pady=5, padx=5, sticky="ew")
        
        self.start_button = ttk.Button(control_frame, text="Start Automation", command=self.start_automation, state=tk.DISABLED, bootstyle="success")
        self.start_button.grid(row=1, column=1, pady=5, padx=5, sticky="ew")
        
        self.pause_button = ttk.Button(control_frame, text="Pause Automation", command=self.pause_automation, state=tk.DISABLED, bootstyle="warning")
        self.pause_button.grid(row=2, column=0, pady=5, padx=5, sticky="ew")
        
        self.resume_button = ttk.Button(control_frame, text="Resume Automation", command=self.resume_automation, state=tk.DISABLED, bootstyle="success")
        self.resume_button.grid(row=2, column=1, pady=5, padx=5, sticky="ew")
        
        # Settings Frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding=10)
        settings_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Row Count Input
        ttk.Label(settings_frame, text="Rows to Process:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.row_count_entry = ttk.Entry(settings_frame, state=tk.DISABLED)
        self.row_count_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # Encoding selection
        ttk.Label(settings_frame, text="CSV Encoding:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        encoding_options = ['utf-8', 'latin1', 'ISO-8859-1', 'cp1252']
        self.encoding_dropdown = ttk.Combobox(settings_frame, textvariable=self.selected_encoding, values=encoding_options, state="readonly")
        self.encoding_dropdown.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
        # Timeout Input
        ttk.Label(settings_frame, text="Timeout (ms):").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.timeout_entry = ttk.Entry(settings_frame)
        self.timeout_entry.insert(0, str(self.automation_timeout))
        self.timeout_entry.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        
        self.update_timeout_button = ttk.Button(settings_frame, text="Update Timeout", command=self.update_timeout, bootstyle="info")
        self.update_timeout_button.grid(row=2, column=2, padx=5, pady=5)
        
        # Checkboxes
        self.delete_checkbox = ttk.Checkbutton(settings_frame, text="Delete Row After Sent", variable=self.delete_after_sent)
        self.delete_checkbox.grid(row=3, column=0, columnspan=2, pady=5, padx=5, sticky="w")
        
        self.ar_checkbox = ttk.Checkbutton(settings_frame, text="Use Aspect Ratio (--ar) Feature", variable=self.use_ar_feature)
        self.ar_checkbox.grid(row=4, column=0, columnspan=2, pady=5, padx=5, sticky="w")
        
        # Status Log
        log_frame = ttk.LabelFrame(main_frame, text="Status Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.status_log = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=10)
        self.status_log.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Bottom buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.clear_button = ttk.Button(button_frame, text="Clear Log", command=self.clear_log, bootstyle="secondary")
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        self.close_button = ttk.Button(button_frame, text="Close", command=self.close_app, bootstyle="danger")
        self.close_button.pack(side=tk.RIGHT, padx=5)
        
        # Configure grid weights
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        settings_frame.columnconfigure(1, weight=1)
        
    def log(self, message):
        """Logs a message to the status log and the console."""
        self.status_log.insert(tk.END, f"{message}\n")
        self.status_log.see(tk.END)
        print(message)
        self.app.update_status(message)  # Update main app status
        
    def initialize_driver(self):
        if self.driver is None:
            try:
                options = uc.ChromeOptions()
                
                # Add Chrome options
                options.add_argument('--no-first-run')
                options.add_argument('--no-service-autorun')
                options.add_argument('--password-store=basic')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-gpu')
                options.add_argument('--window-size=1920,1080')
                options.add_argument('--disable-blink-features=AutomationControlled')
                
                # Use a custom profile directory in the workspace
                profile_path = os.path.join(os.getcwd(), 'chrome_profile')
                if not os.path.exists(profile_path):
                    os.makedirs(profile_path)
                options.add_argument(f'--user-data-dir={profile_path}')
                
                try:
                    self.driver = uc.Chrome(
                        options=options,
                        suppress_welcome=True
                    )
                    self.driver.set_page_load_timeout(30)
                    self.driver.set_script_timeout(30)
                    self.log("Web driver initialized successfully.")
                    return self.driver
                except Exception as chrome_error:
                    self.log(f"Chrome initialization error: {chrome_error}")
                    if self.driver:
                        try:
                            self.driver.quit()
                        except:
                            pass
                    self.driver = None
                    raise
            except Exception as e:
                self.log(f"Failed to initialize driver: {e}")
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                self.driver = None
                return None
        return self.driver
        
    def load_csv(self):
        file_path = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
        if file_path:
            try:
                # Try to load with the selected encoding
                encoding = self.selected_encoding.get()
                try:
                    self.csv_data = pd.read_csv(file_path, encoding=encoding)
                    self.log(f"Loaded CSV with {encoding} encoding")
                except UnicodeDecodeError:
                    # If the selected encoding fails, try common alternatives
                    encodings_to_try = ['latin1', 'ISO-8859-1', 'cp1252']
                    if encoding in encodings_to_try:
                        encodings_to_try.remove(encoding)
                    
                    for alt_encoding in encodings_to_try:
                        try:
                            self.csv_data = pd.read_csv(file_path, encoding=alt_encoding)
                            self.log(f"Selected encoding failed. Loaded with {alt_encoding} encoding instead")
                            self.selected_encoding.set(alt_encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        raise ValueError("Could not decode file with any common encoding.")
                
                self.current_row = 0
                self.rows_to_process = len(self.csv_data)
                self.row_count_entry.config(state=tk.NORMAL)
                self.row_count_entry.delete(0, tk.END)
                self.row_count_entry.insert(0, str(self.rows_to_process))
                self.submit_button.config(state=tk.NORMAL)
                self.start_button.config(state=tk.NORMAL)
                self.loaded_csv_path = file_path
                self.log(f"Loaded {len(self.csv_data)} rows from CSV")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load CSV file: {e}")
                self.log(f"Failed to load CSV: {e}")
                self.start_button.config(state=tk.DISABLED)
                self.row_count_entry.config(state=tk.DISABLED)
        else:
            self.log("No CSV loaded")
            
    def update_csv_file(self):
        if self.loaded_csv_path and self.csv_data is not None:
            try:
                self.csv_data.to_csv(self.loaded_csv_path, index=False)
                self.log(f"CSV updated at: {self.loaded_csv_path}")
            except Exception as e:
                self.log(f"Failed to update CSV: {e}")
                
    def solve_turnstile_captcha(self):
        try:
            sitekey = self.driver.find_element(By.CLASS_NAME, "cf-turnstile").get_attribute('data-sitekey')
            page_url = self.driver.current_url
            
            captcha_data = {
                "method": "turnstile",
                "key": self.API_KEY,
                "sitekey": sitekey,
                "pageurl": page_url,
                "json": 1
            }
            
            response = requests.post("http://2captcha.com/in.php", data=captcha_data)
            captcha_id = response.json().get('request')
            
            result_url = f"http://2captcha.com/res.php?key={self.API_KEY}&action=get&id={captcha_id}&json=1"
            captcha_solution = None
            
            for _ in range(20):
                time.sleep(5)
                result = requests.get(result_url)
                result_data = result.json()
                if result_data['status'] == 1:
                    captcha_solution = result_data['request']
                    break
                    
            if captcha_solution:
                self.driver.execute_script(f'document.getElementById("cf-turnstile-response").value = "{captcha_solution}";')
                self.log(f"CAPTCHA Solved: {captcha_solution}")
            else:
                self.log("Failed to solve CAPTCHA.")
        except Exception as e:
            self.log(f"Error during CAPTCHA solving: {e}")
            
    def make_call(self):
        if self.call_count < 2:
            try:
                client = Client(self.account_sid, self.auth_token)
                execution = client.studio.v2.flows(self.flow_sid).executions.create(
                    to=self.to_phone_number,
                    from_=self.from_twilio_number
                )
                self.call_count += 1
                self.log(f"Flow execution started. SID: {execution.sid}. Call count: {self.call_count}")
            except Exception as e:
                self.log(f"Failed to initiate call: {e}")
        else:
            self.log(f"Call limit exceeded. Current call count: {self.call_count}")
            
    def submit_next_row(self):
        if self.csv_data is not None and self.current_row < min(self.rows_to_process, len(self.csv_data)):
            try:
                if 'input_data' not in self.csv_data.columns:
                    raise ValueError("CSV file missing 'input_data' column")
                    
                input_value = self.csv_data.iloc[self.current_row]['input_data']
                if pd.isna(input_value):
                    self.log(f"Warning: Empty value found at row {self.current_row}")
                    self.current_row += 1
                    return
                    
                input_value = str(input_value).strip().replace('\n', ' ').replace('\r', ' ')
                if not input_value:
                    self.log(f"Warning: Empty string found at row {self.current_row}")
                    self.current_row += 1
                    return
                    
                if self.use_ar_feature.get():
                    random_aspect_ratio = random.choice(self.aspect_ratios)
                    input_value_with_ar = f"{input_value} --ar {random_aspect_ratio}"
                    self.log(f"Submitting row {self.current_row}: {input_value_with_ar}")
                else:
                    input_value_with_ar = input_value
                    self.log(f"Submitting row {self.current_row}: {input_value}")
                    
                if not self.driver or not self.driver.current_url:
                    raise Exception("Browser session not active")
                    
                try:
                    textarea = self.driver.find_element(By.XPATH, "/html/body/div/div[1]/div/div[4]/div/div[1]/div/div[1]/div/textarea")
                except Exception as e:
                    raise Exception(f"Failed to find textarea element: {str(e)}")
                    
                textarea.clear()
                textarea.send_keys(input_value_with_ar)
                time.sleep(5)
                
                try:
                    if self.driver.find_elements(By.CLASS_NAME, "cf-turnstile"):
                        self.log("CAPTCHA detected, attempting to solve...")
                        self.solve_turnstile_captcha()
                except Exception as e:
                    self.log(f"CAPTCHA handling error: {str(e)}")
                    
                textarea.send_keys(Keys.ENTER)
                time.sleep(2)
                
                if self.delete_after_sent.get():
                    try:
                        self.csv_data = self.csv_data.drop(index=self.current_row).reset_index(drop=True)
                        self.update_csv_file()
                        self.log("Row successfully deleted from CSV")
                    except Exception as e:
                        self.log(f"Failed to delete row from CSV: {str(e)}")
                else:
                    self.current_row += 1
                    
                self.log(f"Successfully submitted row {self.current_row}")
                
            except Exception as e:
                error_msg = f"Submission failed at row {self.current_row}: {str(e)}"
                self.log(error_msg)
                self.make_call()
                try:
                    with open('error_log.txt', 'a') as f:
                        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {error_msg}\n")
                except Exception as log_error:
                    self.log(f"Failed to write to error log: {str(log_error)}")
        else:
            if self.csv_data is None:
                self.log("No CSV data loaded")
            else:
                self.log(f"All rows processed. Current row: {self.current_row}, Total rows: {min(self.rows_to_process,len(self.csv_data))}")
                
    def pause_automation(self):
        self.paused = True
        self.log("Automation paused.")
        self.pause_button.config(state=tk.DISABLED)
        self.resume_button.config(state=tk.NORMAL)
        
    def resume_automation(self):
        self.paused = False
        self.log("Resuming automation...")
        self.resume_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.NORMAL)
        self.automate_submission()
        
    def open_web_page(self):
        try:
            self.driver = self.initialize_driver()
            if self.driver is None:
                messagebox.showerror("Error", "Failed to initialize Chrome driver")
                return
                
            self.driver.get("https://www.midjourney.com/imagine")
            self.log("Page loaded, ready to submit!")
            self.open_page_button.config(state=tk.DISABLED)
        except Exception as e:
            self.log(f"Failed to open web page: {e}")
            messagebox.showerror("Error", f"Failed to open web page: {e}")
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
                
    def start_automation(self):
        try:
            self.rows_to_process = int(self.row_count_entry.get())
            if not isinstance(self.rows_to_process, int) or self.rows_to_process <= 0:
                messagebox.showerror("Error", "Please enter a valid positive integer for row count.")
                return
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid integer for row count.")
            return
            
        self.automation_active = True
        self.start_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.NORMAL)
        self.log("Automation started...")
        self.status_log.delete(1.0, tk.END)
        self.current_row = 0
        self.call_count = 0
        self.automate_submission()
        
    def stop_automation(self):
        self.automation_active = False
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED)
        self.log("Automation stopped.")
        
    def automate_submission(self):
        if self.automation_active and not self.paused and self.current_row < min(self.rows_to_process, len(self.csv_data)):
            try:
                start_time = time.time()
                for _ in range(10):
                    if not self.automation_active or self.paused:
                        self.log("Automation stopped or paused")
                        break
                        
                    if not self.driver or not self.driver.current_url:
                        raise Exception("Browser session lost")
                        
                    self.submit_next_row()
                    if self.current_row >= min(self.rows_to_process, len(self.csv_data)):
                        self.log("Reached target row count")
                        break
                        
                    time.sleep(self.automation_timeout / 1000)
                    
                elapsed_time = time.time() - start_time
                self.log(f"Batch processing complete. Time taken: {elapsed_time:.2f} seconds")
                
                if self.current_row < min(self.rows_to_process, len(self.csv_data)):
                    self.after(10000, self.automate_submission)
                else:
                    self.stop_automation()
                    self.log("Automation completed successfully")
                    
            except Exception as e:
                error_msg = f"Automation error: {str(e)}"
                self.log(error_msg)
                try:
                    with open('error_log.txt', 'a') as f:
                        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {error_msg}\n")
                except Exception as log_error:
                    self.log(f"Failed to write to error log: {str(log_error)}")
                self.pause_automation()
                
    def close_app(self):
        if self.driver:
            self.driver.quit()
        self.destroy()
        
    def clear_log(self):
        self.status_log.delete(1.0, tk.END)
        
    def update_timeout(self):
        try:
            self.automation_timeout = int(self.timeout_entry.get())
            self.log(f"Timeout updated to: {self.automation_timeout} ms")
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid integer for timeout.") 