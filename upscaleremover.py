import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

class DuplicateRemoverApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Duplicate Remover")
        self.root.geometry("600x400")
        
        # Variables to store folder paths
        self.folder_one = tk.StringVar()
        self.folder_two = tk.StringVar()
        
        self.create_widgets()
    
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Warning label
        warning_label = ttk.Label(
            main_frame,
            text="⚠️ WARNING: Duplicates will be removed from Folder One!",
            foreground="red",
            font=("Arial", 12, "bold")
        )
        warning_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Folder One selection
        ttk.Label(main_frame, text="Folder One (Source):").grid(row=1, column=0, sticky=tk.W)
        ttk.Entry(main_frame, textvariable=self.folder_one, width=50).grid(row=1, column=1, padx=5)
        ttk.Button(main_frame, text="Browse", command=self.browse_folder_one).grid(row=1, column=2)
        
        # Folder Two selection
        ttk.Label(main_frame, text="Folder Two (Reference):").grid(row=2, column=0, sticky=tk.W, pady=10)
        ttk.Entry(main_frame, textvariable=self.folder_two, width=50).grid(row=2, column=1, padx=5, pady=10)
        ttk.Button(main_frame, text="Browse", command=self.browse_folder_two).grid(row=2, column=2, pady=10)
        
        # Description
        description = """
        How it works:
        1. Select two folders using the Browse buttons
        2. The app will find images with matching names (ignoring extensions)
        3. Matching images will be REMOVED from Folder One
        4. Folder Two remains unchanged
        """
        desc_label = ttk.Label(main_frame, text=description, justify=tk.LEFT)
        desc_label.grid(row=3, column=0, columnspan=3, pady=20, sticky=tk.W)
        
        # Process button
        process_btn = ttk.Button(
            main_frame,
            text="Remove Duplicates",
            command=self.process_folders,
            style="Accent.TButton"
        )
        process_btn.grid(row=4, column=0, columnspan=3, pady=20)
        
        # Style for accent button
        style = ttk.Style()
        style.configure("Accent.TButton", font=("Arial", 11, "bold"))
    
    def browse_folder_one(self):
        folder = filedialog.askdirectory()
        if folder:
            self.folder_one.set(folder)
    
    def browse_folder_two(self):
        folder = filedialog.askdirectory()
        if folder:
            self.folder_two.set(folder)
    
    def process_folders(self):
        folder_one = self.folder_one.get()
        folder_two = self.folder_two.get()
        
        if not folder_one or not folder_two:
            messagebox.showerror("Error", "Please select both folders!")
            return
            
        if messagebox.askyesno("Confirm", 
                              "This will permanently delete duplicate images from Folder One.\n"
                              "Are you sure you want to continue?"):
            removed, errors = remove_duplicate_files(folder_one, folder_two)
            
            if errors:
                messagebox.showerror("Error", "\n".join(errors))
            else:
                messagebox.showinfo("Success", f"Successfully removed {removed} duplicate images from Folder One!")

def remove_duplicate_files(folder_one, folder_two):
    """
    Removes duplicate files (by name without extension) from Folder One.

    Args:
        folder_one (str): Path to the first folder.
        folder_two (str): Path to the second folder.

    Returns:
        tuple: (removed_count, error_messages)
    """
    removed_count = 0
    error_messages = []

    if not folder_one or not folder_two:
        error_messages.append("Folders not selected.")
        return removed_count, error_messages

    try:
        files_in_folder_one = {}  # Store names without extension as keys
        for filename in os.listdir(folder_one):
            name_without_ext = os.path.splitext(filename)[0]
            files_in_folder_one[name_without_ext] = filename

        files_in_folder_two = set()
        for filename in os.listdir(folder_two):
            name_without_ext = os.path.splitext(filename)[0]
            files_in_folder_two.add(name_without_ext)

        duplicates = set(files_in_folder_one.keys()).intersection(files_in_folder_two)

        for name in duplicates:
            filename_to_remove = files_in_folder_one.get(name)
            file_path = os.path.join(folder_one, filename_to_remove)
            if os.path.isfile(file_path):
                os.remove(file_path)
                removed_count += 1

    except Exception as e:
         error_messages.append(f"Error processing files: {e}")

    return removed_count, error_messages

if __name__ == '__main__':
    root = tk.Tk()
    app = DuplicateRemoverApp(root)
    root.mainloop()