import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import threading
from PIL import Image, ImageTk
import math
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue

# Increase PIL's decompression bomb limit to handle large images safely
Image.MAX_IMAGE_PIXELS = None  # Remove the limit entirely, or set to a higher value like 200000000


class ImageResizerFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        self.source_folder = ""
        self.output_folder = ""
        self.resize_mode = "percentage"  # "percentage" or "dimensions"
        self.maintain_aspect = True
        self.image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp']
        self.is_processing = False
        self.executor = None
        self.task_queue = queue.Queue()
        self.create_widgets()

    def create_widgets(self):
        # Main frame layout with improved styling
        main_frame = ttk.LabelFrame(self, text="Image Resizer", padding=15, bootstyle="info")
        main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # Source folder selection
        source_frame = ttk.Frame(main_frame)
        source_frame.pack(pady=10, fill=tk.X)

        ttk.Label(source_frame, text="Source Folder:", font=("Helvetica", 12, "bold")).grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=8)
        self.source_entry = ttk.Entry(source_frame, font=("Helvetica", 12))
        self.source_entry.grid(row=0, column=1, padx=5, pady=8, sticky=tk.EW)
        ttk.Button(source_frame, text="Browse", command=self.browse_source_folder,
                  bootstyle="success-outline").grid(row=0, column=2, padx=5, pady=8)

        source_frame.columnconfigure(1, weight=1)

        # Output folder selection
        output_frame = ttk.Frame(main_frame)
        output_frame.pack(pady=10, fill=tk.X)

        ttk.Label(output_frame, text="Output Folder:", font=("Helvetica", 12, "bold")).grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=8)
        self.output_entry = ttk.Entry(output_frame, font=("Helvetica", 12))
        self.output_entry.grid(row=0, column=1, padx=5, pady=8, sticky=tk.EW)
        ttk.Button(output_frame, text="Browse", command=self.browse_output_folder,
                  bootstyle="success-outline").grid(row=0, column=2, padx=5, pady=8)

        output_frame.columnconfigure(1, weight=1)

        # Worker settings
        worker_frame = ttk.Frame(main_frame)
        worker_frame.pack(pady=10, fill=tk.X)

        ttk.Label(worker_frame, text="Worker Threads:", font=("Helvetica", 12, "bold")).grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=8)
        self.worker_var = tk.IntVar(value=4)  # Default to 4 workers
        worker_spinbox = ttk.Spinbox(worker_frame, from_=1, to=16,
                                   textvariable=self.worker_var, width=10)
        worker_spinbox.grid(row=0, column=1, padx=5, pady=8, sticky=tk.W)
        ttk.Label(worker_frame, text="(Higher values may improve performance but use more CPU)").grid(
            row=0, column=2, sticky=tk.W, padx=5)

        # Resize mode selection
        mode_frame = ttk.LabelFrame(main_frame, text="Resize Mode", padding=10)
        mode_frame.pack(pady=15, fill=tk.X)

        self.mode_var = tk.StringVar(value="percentage")
        ttk.Radiobutton(mode_frame, text="Resize by Percentage", variable=self.mode_var,
                       value="percentage", command=self.on_mode_change).pack(anchor=tk.W, pady=5)
        ttk.Radiobutton(mode_frame, text="Resize by Dimensions", variable=self.mode_var,
                       value="dimensions", command=self.on_mode_change).pack(anchor=tk.W, pady=5)

        # Resize options frame
        self.options_frame = ttk.Frame(main_frame)
        self.options_frame.pack(pady=15, fill=tk.X)

        # Percentage resize options
        self.percentage_frame = ttk.Frame(self.options_frame)
        self.percentage_frame.pack(fill=tk.X)

        ttk.Label(self.percentage_frame, text="Resize Percentage:", font=("Helvetica", 12, "bold")).grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=8)
        self.percentage_var = tk.IntVar(value=50)
        percentage_spinbox = ttk.Spinbox(self.percentage_frame, from_=1, to=500,
                                       textvariable=self.percentage_var, width=10)
        percentage_spinbox.grid(row=0, column=1, padx=5, pady=8, sticky=tk.W)
        ttk.Label(self.percentage_frame, text="%").grid(row=0, column=2, sticky=tk.W, padx=5)

        # Dimensions resize options
        self.dimensions_frame = ttk.Frame(self.options_frame)

        # Aspect ratio checkbox (only for dimensions mode)
        self.aspect_var = tk.BooleanVar(value=True)
        aspect_checkbox = ttk.Checkbutton(self.dimensions_frame, text="Maintain Aspect Ratio",
                                        variable=self.aspect_var, bootstyle="primary",
                                        command=self.on_aspect_change)
        aspect_checkbox.grid(row=0, column=0, columnspan=3, sticky=tk.W, padx=5, pady=10)

        # Container for dimension inputs
        self.dimension_inputs_frame = ttk.Frame(self.dimensions_frame)
        self.dimension_inputs_frame.grid(row=1, column=0, columnspan=3, sticky=tk.EW, pady=5)

        # Width input
        self.width_label = ttk.Label(self.dimension_inputs_frame, text="Width:", font=("Helvetica", 12, "bold"))
        self.width_var = tk.IntVar(value=800)
        self.width_spinbox = ttk.Spinbox(self.dimension_inputs_frame, from_=1, to=10000,
                                       textvariable=self.width_var, width=10)
        self.width_px_label = ttk.Label(self.dimension_inputs_frame, text="px")

        # Height input
        self.height_label = ttk.Label(self.dimension_inputs_frame, text="Height:", font=("Helvetica", 12, "bold"))
        self.height_var = tk.IntVar(value=600)
        self.height_spinbox = ttk.Spinbox(self.dimension_inputs_frame, from_=1, to=10000,
                                        textvariable=self.height_var, width=10)
        self.height_px_label = ttk.Label(self.dimension_inputs_frame, text="px")

        # Max dimension input (for aspect ratio mode)
        self.max_label = ttk.Label(self.dimension_inputs_frame, text="Max Dimension:", font=("Helvetica", 12, "bold"))
        self.max_var = tk.IntVar(value=800)
        self.max_spinbox = ttk.Spinbox(self.dimension_inputs_frame, from_=1, to=10000,
                                     textvariable=self.max_var, width=10)
        self.max_px_label = ttk.Label(self.dimension_inputs_frame, text="px")
        self.max_info_label = ttk.Label(self.dimension_inputs_frame, text="(longest side will be this size)", 
                                      font=("Helvetica", 9), foreground="gray")

        # Quality settings
        quality_frame = ttk.LabelFrame(main_frame, text="Quality Settings", padding=10)
        quality_frame.pack(pady=15, fill=tk.X)

        ttk.Label(quality_frame, text="JPEG Quality:", font=("Helvetica", 12, "bold")).grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=8)
        self.quality_var = tk.IntVar(value=95)
        quality_spinbox = ttk.Spinbox(quality_frame, from_=1, to=100,
                                    textvariable=self.quality_var, width=10)
        quality_spinbox.grid(row=0, column=1, padx=5, pady=8, sticky=tk.W)
        ttk.Label(quality_frame, text="(1-100, higher = better quality)").grid(
            row=0, column=2, sticky=tk.W, padx=5)

        # Resampling method
        ttk.Label(quality_frame, text="Resampling Method:", font=("Helvetica", 12, "bold")).grid(
            row=1, column=0, sticky=tk.W, padx=5, pady=8)
        self.resample_var = tk.StringVar(value="LANCZOS")
        resample_combo = ttk.Combobox(quality_frame, textvariable=self.resample_var,
                                    values=["LANCZOS", "BICUBIC", "BILINEAR", "NEAREST"],
                                    state="readonly", width=15)
        resample_combo.grid(row=1, column=1, padx=5, pady=8, sticky=tk.W)
        ttk.Label(quality_frame, text="(LANCZOS recommended for best quality)").grid(
            row=1, column=2, sticky=tk.W, padx=5)

        # Process button
        self.process_btn = ttk.Button(main_frame, text="Start Resizing", command=self.start_resizing,
                               bootstyle="primary", width=20)
        self.process_btn.pack(pady=20)

        # Progress section
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding=10)
        progress_frame.pack(pady=15, fill=tk.X)

        self.status_label = ttk.Label(progress_frame, text="Ready to resize images",
                                    font=("Helvetica", 10))
        self.status_label.pack(anchor="w", padx=5, pady=5)

        self.progress_var = tk.IntVar(value=0)
        self.progress_bar = ttk.Progressbar(progress_frame, orient="horizontal",
                                          length=300, mode="determinate",
                                          variable=self.progress_var, bootstyle="success")
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
        
        # Stats label
        self.stats_label = ttk.Label(progress_frame, text="", font=("Helvetica", 10))
        self.stats_label.pack(anchor="w", padx=5, pady=5)

        # Initialize with percentage mode
        self.on_mode_change()

    def on_mode_change(self):
        """Handle resize mode change"""
        if self.mode_var.get() == "percentage":
            self.dimensions_frame.pack_forget()
            self.percentage_frame.pack(fill=tk.X)
        else:
            self.percentage_frame.pack_forget()
            self.dimensions_frame.pack(fill=tk.X)
            self.on_aspect_change()  # Update dimension inputs layout

    def on_aspect_change(self):
        """Handle aspect ratio checkbox change"""
        # Clear all widgets first
        for widget in self.dimension_inputs_frame.winfo_children():
            widget.grid_forget()
        
        if self.aspect_var.get():
            # Show only max dimension input when maintaining aspect ratio
            self.max_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=8)
            self.max_spinbox.grid(row=0, column=1, padx=5, pady=8, sticky=tk.W)
            self.max_px_label.grid(row=0, column=2, sticky=tk.W, padx=5)
            self.max_info_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(0, 8))
        else:
            # Show both width and height inputs when not maintaining aspect ratio
            self.width_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=8)
            self.width_spinbox.grid(row=0, column=1, padx=5, pady=8, sticky=tk.W)
            self.width_px_label.grid(row=0, column=2, sticky=tk.W, padx=5)
            
            self.height_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=8)
            self.height_spinbox.grid(row=1, column=1, padx=5, pady=8, sticky=tk.W)
            self.height_px_label.grid(row=1, column=2, sticky=tk.W, padx=5)

    def browse_source_folder(self):
        folder_selected = filedialog.askdirectory()
        if folder_selected:
            self.source_folder = folder_selected
            self.source_entry.delete(0, tk.END)
            self.source_entry.insert(0, folder_selected)
            self.status_label.config(text=f"Selected source: {os.path.basename(folder_selected)}")

    def browse_output_folder(self):
        folder_selected = filedialog.askdirectory()
        if folder_selected:
            self.output_folder = folder_selected
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, folder_selected)
            self.status_label.config(text=f"Selected output: {os.path.basename(folder_selected)}")

    def start_resizing(self):
        source_folder = self.source_entry.get()
        output_folder = self.output_entry.get()

        if not source_folder or not output_folder:
            messagebox.showerror("Error", "Please select both source and output folders.")
            return

        if not os.path.exists(source_folder):
            messagebox.showerror("Error", "Source folder does not exist.")
            return

        if not os.path.exists(output_folder):
            try:
                os.makedirs(output_folder)
            except Exception as e:
                messagebox.showerror("Error", f"Could not create output folder: {str(e)}")
                return
                
        if self.is_processing:
            messagebox.showinfo("Processing", "Resizing operation is already in progress.")
            return

        self.progress_var.set(0)
        self.progress_bar["value"] = 0
        self.status_label.config(text="Starting image resizing process...")
        self.stats_label.config(text="")
        self.process_btn.config(text="Processing...", state="disabled")
        self.is_processing = True
        
        # Update worker status in the app
        if hasattr(self.app, 'update_worker_status'):
            self.app.update_worker_status('processing')

        threading.Thread(target=self.resize_images, args=(source_folder, output_folder), daemon=True).start()

    def resize_images(self, source_folder, output_folder):
        try:
            # Configure PIL to handle large images safely
            # Set a reasonable limit for very large images (200MP)
            original_limit = Image.MAX_IMAGE_PIXELS
            Image.MAX_IMAGE_PIXELS = 200000000
            
            # Get all image files
            image_files = []
            for filename in os.listdir(source_folder):
                if any(filename.lower().endswith(ext) for ext in self.image_extensions):
                    image_files.append(filename)

            if not image_files:
                self.status_label.config(text="No image files found in source folder")
                messagebox.showinfo("Info", "No image files found in the source folder.")
                self.reset_ui()
                return

            total_files = len(image_files)
            processed_files = 0
            successful_files = 0
            failed_files = 0
            errors = []
            
            # Get worker count
            worker_count = max(1, min(16, self.worker_var.get()))
            
            # Create executor for parallel processing
            with ThreadPoolExecutor(max_workers=worker_count) as executor:
                # Submit tasks to the executor
                futures = {
                    executor.submit(self.process_single_image, source_folder, output_folder, filename): filename
                    for filename in image_files
                }
                
                # Process results as they complete
                for future in as_completed(futures):
                    filename = futures[future]
                    processed_files += 1
                    
                    try:
                        result = future.result()
                        if result is True:
                            successful_files += 1
                        else:
                            failed_files += 1
                            errors.append(f"Error processing {filename}: {result}")
                    except Exception as e:
                        failed_files += 1
                        error_msg = f"Error processing {filename}: {str(e)}"
                        errors.append(error_msg)
                        self.app.update_status(error_msg)
                    
                    # Update progress
                    progress = int((processed_files / total_files) * 100)
                    self.progress_var.set(progress)
                    self.status_label.config(text=f"Processing: {filename} ({processed_files}/{total_files})")
                    self.stats_label.config(text=f"Successful: {successful_files} | Failed: {failed_files}")
                    self.app.update_status(f"Resized: {processed_files}/{total_files} images")

            # Final status update
            if errors:
                error_summary = f"Completed with {len(errors)} errors. Processed {processed_files}/{total_files} files."
                self.status_label.config(text=error_summary)
                messagebox.showwarning("Completed with Errors", 
                                     f"{error_summary}\n\nFirst few errors:\n" + "\n".join(errors[:3]))
            else:
                success_msg = f"Successfully resized {processed_files} images!"
                self.status_label.config(text=success_msg)
                messagebox.showinfo("Success", success_msg)

        except Exception as e:
            error_msg = f"An unexpected error occurred: {str(e)}"
            self.app.update_status(error_msg)
            self.status_label.config(text="Process failed - unexpected error")
            messagebox.showerror("Error", error_msg)
        finally:
            # Restore original PIL image size limit
            Image.MAX_IMAGE_PIXELS = original_limit
            self.reset_ui()
            
    def reset_ui(self):
        """Reset UI after processing is complete"""
        self.is_processing = False
        self.process_btn.config(text="Start Resizing", state="normal")
        
        # Update worker status in the app
        if hasattr(self.app, 'update_worker_status'):
            self.app.update_worker_status('active')
            
    def process_single_image(self, source_folder, output_folder, filename):
        """Process a single image in a worker thread"""
        try:
            input_path = os.path.join(source_folder, filename)
            output_path = os.path.join(output_folder, filename)

            # Open image
            with Image.open(input_path) as img:
                # Convert RGBA to RGB for JPEG if needed
                if img.mode == 'RGBA' and filename.lower().endswith(('.jpg', '.jpeg')):
                    # Create white background
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])  # Use alpha channel as mask
                    img = background

                # Calculate new size
                if self.mode_var.get() == "percentage":
                    percentage = self.percentage_var.get() / 100.0
                    new_width = int(img.width * percentage)
                    new_height = int(img.height * percentage)
                else:
                    if self.aspect_var.get():
                        # Use max dimension approach
                        max_dimension = self.max_var.get()
                        aspect_ratio = img.width / img.height
                        
                        if img.width > img.height:
                            # Width is the longer side
                            new_width = max_dimension
                            new_height = int(max_dimension / aspect_ratio)
                        else:
                            # Height is the longer side
                            new_height = max_dimension
                            new_width = int(max_dimension * aspect_ratio)
                    else:
                        # Use exact width and height
                        new_width = self.width_var.get()
                        new_height = self.height_var.get()

                # Resize image
                resample_method = getattr(Image.Resampling, self.resample_var.get())
                resized_img = img.resize((new_width, new_height), resample_method)

                # Save image with appropriate settings
                quality = self.quality_var.get()
                if filename.lower().endswith(('.jpg', '.jpeg')):
                    resized_img.save(output_path, 'JPEG', quality=quality, optimize=True)
                elif filename.lower().endswith('.png'):
                    resized_img.save(output_path, 'PNG', optimize=True)
                else:
                    resized_img.save(output_path)

            return True
        except Exception as e:
            return str(e)