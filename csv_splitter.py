import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import pandas as pd
import os
import math

class CSVSplitterFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        self.create_widgets()

    def create_widgets(self):
        # Main frame layout with improved styling
        main_frame = ttk.LabelFrame(self, text="CSV Splitter V2", padding=15, bootstyle="info")
        main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
        
        # Input File selection
        file_frame = ttk.Frame(main_frame)
        file_frame.pack(pady=5, fill=tk.X)

        ttk.Label(file_frame, text="Input CSV File:", font=("Helvetica", 12, "bold")).pack(side=tk.LEFT, padx=5)
        
        self.file_path = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path, width=50)
        self.file_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        
        self.browse_button = ttk.Button(file_frame, text="Browse", command=self.browse_input_file, bootstyle=SUCCESS)
        self.browse_button.pack(side=tk.LEFT, padx=5)

        # Output folder selection
        output_frame = ttk.Frame(main_frame)
        output_frame.pack(pady=5, fill=tk.X)

        ttk.Label(output_frame, text="Output Folder:", font=("Helvetica", 12, "bold")).pack(side=tk.LEFT, padx=5)
        
        self.output_path = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_path, width=50)
        self.output_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        
        self.output_browse_button = ttk.Button(output_frame, text="Browse", command=self.browse_output_folder, bootstyle=SUCCESS)
        self.output_browse_button.pack(side=tk.LEFT, padx=5)
        
        # Progress section
        progress_frame = ttk.LabelFrame(main_frame, text="Processing Status", padding=10)
        progress_frame.pack(pady=15, fill=tk.X, padx=5)

        self.status_label = ttk.Label(progress_frame, text="Ready to process", font=("Helvetica", 10))
        self.status_label.pack(anchor="w", padx=5, pady=5)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, orient="horizontal", 
                                         length=300, mode="determinate", 
                                         variable=self.progress_var, bootstyle="success")
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # Description
        desc_frame = ttk.LabelFrame(main_frame, text="Information", padding=10)
        desc_frame.pack(pady=15, fill=tk.X, padx=5)
        
        description = """
        This tool splits large CSV files into smaller parts while keeping each part under 1MB.
        
        How it works:
        1. Select your input CSV file
        2. Choose an output folder for the split files
        3. Click 'Split CSV' to begin processing
        4. Files will be named as original_part1.csv, original_part2.csv, etc.
        """
        desc_label = ttk.Label(desc_frame, text=description, justify=tk.LEFT, 
                             font=("Helvetica", 10), wraplength=500)
        desc_label.pack(pady=5)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        self.split_button = ttk.Button(button_frame, 
                                     text="Split CSV", 
                                     command=self.split_csv,
                                     bootstyle="primary",
                                     width=20)
        self.split_button.pack(pady=5)

    def browse_input_file(self):
        filename = filedialog.askopenfilename(
            title="Select CSV file",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.file_path.set(filename)
            # Set default output directory to input file's directory with input file name
            input_dir = os.path.dirname(filename)
            input_filename = os.path.splitext(os.path.basename(filename))[0]
            default_output_dir = os.path.join(input_dir, input_filename)
            if not self.output_path.get():
                self.output_path.set(default_output_dir)
            self.status_label.config(text=f"Selected file: {os.path.basename(filename)}")

    def browse_output_folder(self):
        folder = filedialog.askdirectory(title="Select Output Folder")
        if folder:
            self.output_path.set(folder)
            self.status_label.config(text=f"Output folder: {os.path.basename(folder)}")

    def split_csv(self):
        file_path = self.file_path.get()
        output_dir = self.output_path.get()

        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("Error", "Please select a valid CSV file")
            return

        if not output_dir or not os.path.exists(output_dir):
            messagebox.showerror("Error", "Please select a valid output folder")
            return

        try:
            self.split_button.config(state=tk.DISABLED)
            self.progress_var.set(0)
            self.status_label.config(text="Reading CSV file...")
            
            # Read the CSV file
            df = pd.read_csv(file_path)
            total_rows = len(df)
            
            # Calculate the number of rows per file to stay under 1MB
            sample_size = min(1000, total_rows)
            sample_df = df.head(sample_size)
            sample_size_bytes = sample_df.memory_usage(deep=True).sum()
            bytes_per_row = sample_size_bytes / sample_size
            rows_per_file = int((1024 * 1024) / bytes_per_row)  # 1MB in bytes
            
            # Calculate number of chunks needed
            num_chunks = math.ceil(total_rows / rows_per_file)
            
            # Get base name from input file
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            
            # Split the file
            for i in range(num_chunks):
                start_idx = i * rows_per_file
                end_idx = min((i + 1) * rows_per_file, total_rows)
                
                chunk_df = df.iloc[start_idx:end_idx]
                output_file = os.path.join(output_dir, f"{base_name}_part{i+1}.csv")
                chunk_df.to_csv(output_file, index=False)
                
                # Update progress
                progress = ((i + 1) / num_chunks) * 100
                self.progress_var.set(progress)
                self.status_label.config(text=f"Processing part {i+1} of {num_chunks}")
                self.update_idletasks()
            
            self.status_label.config(text=f"Complete! Split into {num_chunks} files")
            messagebox.showinfo("Success", f"CSV file has been split into {num_chunks} files\nLocation: {output_dir}")
            
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
            self.status_label.config(text="Error occurred")
        finally:
            self.split_button.config(state=tk.NORMAL)

# Keep the original app for standalone usage
class CSVSplitterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("CSV Splitter V2")
        self.root.geometry("600x450")
        
        self.frame = CSVSplitterFrame(root, self)
        self.frame.pack(expand=True, fill=tk.BOTH)

def main():
    root = tk.Tk()
    app = CSVSplitterApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 